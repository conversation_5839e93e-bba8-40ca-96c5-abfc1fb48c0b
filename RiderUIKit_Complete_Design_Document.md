# RiderUIKit SDK 完整设计文档

## 1. 概述

RiderUIKit 是基于高德地图SDK的自定义UI组件库，提供完全可配置的地图应用界面解决方案。SDK采用组件化设计，隐藏底层地图SDK复杂性，通过UI组件交互自动获取数据，支持主题配置和日夜模式切换。

## 2. 设计原则

### 2.1 核心设计理念
- **SDK数据驱动**：所有数据来源于地图SDK，确保准确性和一致性
- **UI组件交互**：用户通过UI操作获取数据，无需直接调用SDK API
- **智能错误处理**：数据缺失时提供友好提示和解决方案
- **主题可配置**：支持颜色、图标的完全自定义和日夜模式
- **组件间协作**：通过数据管理器实现组件间自动数据共享

### 2.2 API开放策略
- **每个自定义View只有一个统一的Callback接口**
- **只开放需要上层业务逻辑处理的点击事件**
- **内部UI更新的点击事件不开放，由组件内部自动处理**
- **主题配置完全开放，支持运行时动态更新**

## 3. 架构设计

### 3.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    RiderUIKit SDK                           │
├─────────────────────────────────────────────────────────────┤
│  UI组件层                                                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ RiderMapView│ │RiderSearchView│ │RiderRouteView│ │其他组件 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│  数据管理层                                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           RiderUIDataManager                            │ │
│  │  - 组件间数据共享                                        │ │
│  │  - 内存数据缓存                                          │ │
│  │  - 智能错误处理                                          │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  主题管理层                                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           RiderUIThemeManager                           │ │
│  │  - 主题配置管理                                          │ │
│  │  - 日夜模式切换                                          │ │
│  │  - 全局主题应用                                          │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  地图SDK集成层                                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              RiderMap SDK                               │ │
│  │  - 地图显示                                              │ │
│  │  - 搜索服务                                              │ │
│  │  - 导航服务                                              │ │
│  │  - 定位服务                                              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 数据流向
```
用户操作 → UI组件 → 内部SDK调用 → 数据管理器 → 其他组件自动更新
```

### 3.3 组件间协作机制
- **数据管理器**：RiderUIDataManager负责组件间数据共享
- **自动注册**：组件创建时自动注册到数据管理器
- **数据同步**：一个组件的数据更新会自动通知其他相关组件
- **智能提示**：数据缺失时组件会引导用户使用其他组件获取数据

## 4. 核心组件设计

### 4.1 RiderMapView - 地图显示组件

#### 4.1.1 功能特性
- 地图显示和基础交互
- 当前位置自动显示
- 搜索结果自动展示
- 导航路线自动显示
- 标记点自动管理

#### 4.1.2 内部自动处理的功能
- **定位按钮点击**：内部直接获取当前位置并更新地图显示
- **缩放按钮点击**：内部直接进行地图缩放操作
- **图层切换按钮**：内部直接切换地图显示图层
- **指南针按钮**：内部直接调整地图方向
- **数据自动更新**：根据其他组件的数据变化自动更新地图内容

#### 4.1.3 开放的交互事件
- **地图点击**：用户点击地图空白区域，上层可用于位置选择
- **标记点击**：用户点击地图上的标记点，上层可用于显示详情

#### 4.1.4 API设计
```kotlin
class RiderMapView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    
    // 统一回调接口
    fun setMapCallback(callback: MapCallback)
    
    interface MapCallback {
        fun onMapClick(latitude: Double, longitude: Double)
        fun onMarkerClick(markerId: String)
    }
    
    // 主题配置
    fun updateTheme(config: MapThemeConfig)
    fun applyStyle(@StyleRes styleRes: Int)
}
```

### 4.2 RiderSearchView - 搜索组件

#### 4.2.1 功能特性
- 关键字搜索功能
- 搜索历史自动管理
- 搜索建议自动显示
- POI分类搜索支持

#### 4.2.2 内部自动处理的功能
- **清空按钮点击**：内部直接清空搜索框内容
- **语音搜索按钮点击**：内部直接启动语音识别并填入搜索框
- **搜索建议点击**：内部直接将建议内容填入搜索框
- **历史删除按钮点击**：内部直接删除对应历史项并更新显示
- **搜索执行**：内部自动调用地图SDK进行搜索并显示结果

#### 4.2.3 开放的交互事件
- **搜索提交**：用户提交搜索请求，上层可进行搜索前的验证或处理
- **搜索结果项点击**：用户选择搜索结果，上层可进行后续业务处理
- **历史项点击**：用户选择历史搜索，上层可进行相应处理

#### 4.2.4 API设计
```kotlin
class RiderSearchView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {
    
    // 统一回调接口
    fun setSearchCallback(callback: SearchCallback)
    
    interface SearchCallback {
        fun onSearchSubmitted(query: String)
        fun onSearchResultItemClick(item: SearchResultItem)
        fun onHistoryItemClick(query: String)
    }
    
    // 主题配置
    fun updateTheme(config: SearchThemeConfig)
    fun applyStyle(@StyleRes styleRes: Int)
}
```

### 4.3 RiderRouteView - 路线规划组件

#### 4.3.1 功能特性
- 起点终点设置界面
- 路线计算和显示
- 多路线方案选择
- 导航启动入口

#### 4.3.2 内部自动处理的功能
- **起终点交换按钮点击**：内部直接交换起点和终点的显示
- **使用当前位置按钮点击**：内部直接获取当前位置并设为起点
- **清空路线按钮点击**：内部直接清空当前路线显示
- **路线详情展开/收起**：内部直接切换路线详情的显示状态
- **路线计算**：内部自动调用地图SDK计算路线并显示结果

#### 4.3.3 开放的交互事件
- **起点选择按钮点击**：需要上层提供位置选择界面
- **终点选择按钮点击**：需要上层提供位置选择界面
- **计算路线按钮点击**：需要上层处理路线计算的业务逻辑
- **开始导航按钮点击**：需要上层处理导航启动的业务逻辑
- **路线选项点击**：用户选择不同路线方案，需要上层处理

#### 4.3.4 API设计
```kotlin
class RiderRouteView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {
    
    // 统一回调接口
    fun setRouteCallback(callback: RouteCallback)
    
    interface RouteCallback {
        fun onStartLocationButtonClick()
        fun onDestinationButtonClick()
        fun onCalculateRouteButtonClick()
        fun onStartNavigationButtonClick()
        fun onRouteOptionClick(routeIndex: Int)
    }
    
    // 主题配置
    fun updateTheme(config: RouteThemeConfig)
    fun applyStyle(@StyleRes styleRes: Int)
}
```

### 4.4 RiderNavigationView - 导航信息组件

#### 4.4.1 功能特性
- 导航信息实时显示
- 导航控制功能
- 语音播报控制
- 导航状态管理

#### 4.4.2 内部自动处理的功能
- **语音开关按钮点击**：内部直接切换语音播报状态并更新按钮显示
- **测速提醒按钮点击**：内部直接切换测速提醒状态并更新显示
- **导航信息展开/收起**：内部直接切换导航信息的显示状态
- **夜间模式切换**：内部直接切换地图夜间模式
- **导航信息更新**：内部自动接收导航SDK数据并更新显示

#### 4.4.3 开放的交互事件
- **停止导航按钮点击**：需要上层处理导航停止的业务逻辑
- **导航设置按钮点击**：需要上层打开导航设置界面
- **路线总览按钮点击**：需要上层显示路线总览界面
- **上报按钮点击**：需要上层处理路况上报功能

#### 4.4.4 API设计
```kotlin
class RiderNavigationView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {
    
    // 统一回调接口
    fun setNavigationCallback(callback: NavigationCallback)
    
    interface NavigationCallback {
        fun onStopNavigationButtonClick()
        fun onNavigationSettingsButtonClick()
        fun onRouteOverviewButtonClick()
        fun onReportButtonClick()
    }
    
    // 主题配置
    fun updateTheme(config: NavigationThemeConfig)
    fun applyStyle(@StyleRes styleRes: Int)
}
```

## 5. 数据管理设计

### 5.1 RiderUIDataManager

#### 5.1.1 设计目标
RiderUIDataManager是SDK内部的数据管理中心，负责组件间的数据共享和同步，对外不开放任何API，完全由SDK内部自动管理。

#### 5.1.2 核心功能
- **组件自动注册**：组件创建时自动注册到数据管理器
- **数据缓存管理**：缓存当前位置、搜索结果、路线信息等数据
- **数据同步通知**：数据更新时自动通知相关组件
- **智能错误处理**：数据缺失时提供友好的错误提示

#### 5.1.3 内部实现机制
```kotlin
object RiderUIDataManager {
    // 数据缓存
    private var currentLocation: LocationInfo? = null
    private var searchResults: List<SearchResultItem> = emptyList()
    private var routeInfo: RouteInfo? = null
    private var navigationInfo: NavigationInfo? = null

    // 组件注册（弱引用避免内存泄漏）
    private val mapViews = mutableSetOf<WeakReference<RiderMapView>>()
    private val searchViews = mutableSetOf<WeakReference<RiderSearchView>>()
    private val routeViews = mutableSetOf<WeakReference<RiderRouteView>>()
    private val navigationViews = mutableSetOf<WeakReference<RiderNavigationView>>()

    // 数据更新通知机制
    internal fun onLocationObtained(location: LocationInfo) {
        currentLocation = location
        notifyMapViews { it.updateLocationDisplay(location) }
        notifyRouteViews { it.updateStartLocation(location) }
    }

    internal fun onSearchCompleted(results: List<SearchResultItem>) {
        searchResults = results
        notifyMapViews { it.updateSearchResults(results) }
        notifySearchViews { it.updateSearchResults(results) }
    }

    internal fun onRouteCalculated(route: RouteInfo) {
        routeInfo = route
        notifyMapViews { it.updateRouteDisplay(route) }
        notifyRouteViews { it.updateRouteInfo(route) }
    }
}
```

### 5.2 数据模型设计

#### 5.2.1 位置信息模型
```kotlin
data class LocationInfo(
    val latitude: Double,
    val longitude: Double,
    val address: String,
    val accuracy: Float = 0f,
    val timestamp: Long = System.currentTimeMillis()
)
```

#### 5.2.2 搜索结果模型
```kotlin
data class SearchResultItem(
    val id: String,
    val name: String,
    val address: String,
    val location: LocationInfo,
    val category: String = "",
    val distance: Float = 0f,
    val rating: Float = 0f
)
```

#### 5.2.3 路线信息模型
```kotlin
data class RouteInfo(
    val routeId: String,
    val startLocation: LocationInfo,
    val endLocation: LocationInfo,
    val distance: Float,
    val duration: Int,
    val strategy: RouteStrategy,
    val polylinePoints: List<LocationInfo>
)

enum class RouteStrategy {
    FASTEST, SHORTEST, AVOID_HIGHWAY, AVOID_TOLL
}
```

## 6. 主题配置设计

### 6.1 主题配置架构

#### 6.1.1 设计目标
- 支持日间/夜间模式自动切换
- 支持颜色和图标的完全自定义
- 支持运行时动态主题更新
- 提供多种配置方式（XML属性、代码配置、样式资源）

#### 6.1.2 主题配置层次
```
全局主题配置
├── 颜色配置（ColorScheme）
├── 图标配置（IconSet）
├── 字体配置（Typography）
└── 间距配置（Spacing）
```

### 6.2 主题配置数据类

#### 6.2.1 地图组件主题配置
```kotlin
data class MapThemeConfig(
    val textPrimary: Int? = null,
    val textSecondary: Int? = null,
    val background: Int? = null,
    val icons: MapIconConfig? = null
)

data class MapIconConfig(
    val locationIcon: Int? = null,
    val markerIcon: Int? = null,
    val zoomInIcon: Int? = null,
    val zoomOutIcon: Int? = null,
    val compassIcon: Int? = null,
    val layerSwitchIcon: Int? = null
)
```

#### 6.2.2 搜索组件主题配置
```kotlin
data class SearchThemeConfig(
    val textPrimary: Int? = null,
    val textHint: Int? = null,
    val background: Int? = null,
    val itemBackground: Int? = null,
    val icons: SearchIconConfig? = null
)

data class SearchIconConfig(
    val searchIcon: Int? = null,
    val clearIcon: Int? = null,
    val voiceIcon: Int? = null,
    val historyIcon: Int? = null,
    val poiIcon: Int? = null,
    val deleteIcon: Int? = null
)
```

#### 6.2.3 路线组件主题配置
```kotlin
data class RouteThemeConfig(
    val textPrimary: Int? = null,
    val textSecondary: Int? = null,
    val buttonBackground: Int? = null,
    val buttonText: Int? = null,
    val icons: RouteIconConfig? = null
)

data class RouteIconConfig(
    val startIcon: Int? = null,
    val endIcon: Int? = null,
    val swapIcon: Int? = null,
    val currentLocationIcon: Int? = null,
    val calculateIcon: Int? = null,
    val navigationIcon: Int? = null,
    val clearIcon: Int? = null
)
```

#### 6.2.4 导航组件主题配置
```kotlin
data class NavigationThemeConfig(
    val textPrimary: Int? = null,
    val textSecondary: Int? = null,
    val background: Int? = null,
    val accent: Int? = null,
    val icons: NavigationIconConfig? = null
)

data class NavigationIconConfig(
    val stopIcon: Int? = null,
    val settingsIcon: Int? = null,
    val overviewIcon: Int? = null,
    val reportIcon: Int? = null,
    val voiceOnIcon: Int? = null,
    val voiceOffIcon: Int? = null,
    val speedCameraIcon: Int? = null
)
```

### 6.3 主题管理器

#### 6.3.1 RiderUIThemeManager
```kotlin
object RiderUIThemeManager {
    enum class ThemeMode { DAY, NIGHT, AUTO }

    // 主题切换
    fun setTheme(mode: ThemeMode)
    fun setGlobalTheme(themeName: String)
    fun getCurrentTheme(): ThemeMode

    // 主题配置管理
    fun registerThemeConfig(themeName: String, componentConfigs: Map<String, Any>)
    fun getThemeConfig(themeName: String, componentName: String): Any?
    fun initializeDefaultThemes(context: Context)

    // 监听器管理
    fun registerThemeListener(listener: ThemeChangeListener)
    fun unregisterThemeListener(listener: ThemeChangeListener)

    interface ThemeChangeListener {
        fun onThemeChanged(isDarkMode: Boolean)
    }
}
```

### 6.4 主题配置构建器

#### 6.4.1 ThemeConfigBuilder
```kotlin
class ThemeConfigBuilder {
    companion object {
        fun mapTheme(block: MapThemeBuilder.() -> Unit): MapThemeConfig
        fun searchTheme(block: SearchThemeBuilder.() -> Unit): SearchThemeConfig
        fun routeTheme(block: RouteThemeBuilder.() -> Unit): RouteThemeConfig
        fun navigationTheme(block: NavigationThemeBuilder.() -> Unit): NavigationThemeConfig
    }

    class MapThemeBuilder {
        private var textPrimary: Int? = null
        private var textSecondary: Int? = null
        private var background: Int? = null
        private val iconBuilder = MapIconBuilder()

        fun textPrimary(color: Int) = apply { textPrimary = color }
        fun textSecondary(color: Int) = apply { textSecondary = color }
        fun background(color: Int) = apply { background = color }
        fun icons(block: MapIconBuilder.() -> Unit) = apply { iconBuilder.block() }

        fun build() = MapThemeConfig(textPrimary, textSecondary, background, iconBuilder.build())
    }

    class MapIconBuilder {
        private var locationIcon: Int? = null
        private var markerIcon: Int? = null
        private var zoomInIcon: Int? = null
        private var zoomOutIcon: Int? = null

        fun locationIcon(resId: Int) = apply { locationIcon = resId }
        fun markerIcon(resId: Int) = apply { markerIcon = resId }
        fun zoomInIcon(resId: Int) = apply { zoomInIcon = resId }
        fun zoomOutIcon(resId: Int) = apply { zoomOutIcon = resId }

        fun build() = MapIconConfig(locationIcon, markerIcon, zoomInIcon, zoomOutIcon)
    }
}
```

### 6.5 资源文件配置

#### 6.5.1 颜色资源配置
```xml
<!-- res/values/colors.xml -->
<resources>
    <!-- 地图组件颜色 -->
    <color name="rider_map_text_primary">#FF000000</color>
    <color name="rider_map_text_secondary">#FF666666</color>
    <color name="rider_map_background">#FFFFFFFF</color>

    <!-- 搜索组件颜色 -->
    <color name="rider_search_text_primary">#FF000000</color>
    <color name="rider_search_text_hint">#FF999999</color>
    <color name="rider_search_background">#FFFFFFFF</color>
    <color name="rider_search_item_background">#FFF5F5F5</color>
</resources>

<!-- res/values-night/colors.xml -->
<resources>
    <!-- 地图组件颜色 - 夜间模式 -->
    <color name="rider_map_text_primary">#FFFFFFFF</color>
    <color name="rider_map_text_secondary">#FFCCCCCC</color>
    <color name="rider_map_background">#FF2A2A2A</color>

    <!-- 搜索组件颜色 - 夜间模式 -->
    <color name="rider_search_text_primary">#FFFFFFFF</color>
    <color name="rider_search_text_hint">#FF888888</color>
    <color name="rider_search_background">#FF2A2A2A</color>
    <color name="rider_search_item_background">#FF3A3A3A</color>
</resources>
```

#### 6.5.2 图标资源配置
```xml
<!-- res/values/drawables.xml -->
<resources>
    <!-- 地图组件图标 -->
    <drawable name="rider_map_location_icon">@drawable/ic_location_day</drawable>
    <drawable name="rider_map_marker_icon">@drawable/ic_marker_day</drawable>
    <drawable name="rider_map_zoom_in_icon">@drawable/ic_zoom_in_day</drawable>
    <drawable name="rider_map_zoom_out_icon">@drawable/ic_zoom_out_day</drawable>
</resources>

<!-- res/values-night/drawables.xml -->
<resources>
    <!-- 地图组件图标 - 夜间模式 -->
    <drawable name="rider_map_location_icon">@drawable/ic_location_night</drawable>
    <drawable name="rider_map_marker_icon">@drawable/ic_marker_night</drawable>
    <drawable name="rider_map_zoom_in_icon">@drawable/ic_zoom_in_night</drawable>
    <drawable name="rider_map_zoom_out_icon">@drawable/ic_zoom_out_night</drawable>
</resources>
```

#### 6.5.3 自定义属性定义
```xml
<!-- res/values/attrs.xml -->
<resources>
    <!-- 地图组件属性 -->
    <declare-styleable name="RiderMapView">
        <attr name="mapTextPrimary" format="color|reference" />
        <attr name="mapTextSecondary" format="color|reference" />
        <attr name="mapBackground" format="color|reference" />
        <attr name="mapLocationIcon" format="reference" />
        <attr name="mapMarkerIcon" format="reference" />
        <attr name="mapZoomInIcon" format="reference" />
        <attr name="mapZoomOutIcon" format="reference" />
    </declare-styleable>

    <!-- 搜索组件属性 -->
    <declare-styleable name="RiderSearchView">
        <attr name="searchTextPrimary" format="color|reference" />
        <attr name="searchTextHint" format="color|reference" />
        <attr name="searchBackground" format="color|reference" />
        <attr name="searchItemBackground" format="color|reference" />
        <attr name="searchIcon" format="reference" />
        <attr name="searchClearIcon" format="reference" />
        <attr name="searchVoiceIcon" format="reference" />
        <attr name="searchHistoryIcon" format="reference" />
    </declare-styleable>
</resources>
```

## 7. 搜索历史管理设计

### 7.1 设计原则

#### 7.1.1 存储方案选择
基于SDK的特性，搜索历史管理采用以下原则：
- **默认使用SharedPreferences**：轻量级，无依赖冲突，适合SDK使用
- **支持自定义存储提供者**：允许宿主应用使用自己的存储方案
- **避免使用Room**：防止与宿主应用的Room版本冲突

#### 7.1.2 存储策略
- **内存缓存**：提高访问性能
- **异步存储**：避免阻塞UI线程
- **数据限制**：限制历史记录数量，防止存储空间过大
- **自动清理**：定期清理过期的历史记录

### 7.2 搜索历史提供者接口

#### 7.2.1 SearchHistoryProvider
```kotlin
interface SearchHistoryProvider {
    fun saveSearchHistory(query: String, location: LocationInfo? = null, resultCount: Int = 0)
    fun getSearchHistory(): List<SearchHistoryItem>
    fun getSearchSuggestions(prefix: String): List<String>
    fun clearSearchHistory()
    fun removeSearchItem(query: String)
    fun getPopularSearches(limit: Int = 10): List<String>
    fun exportHistory(): String
    fun importHistory(jsonData: String): Boolean
}

data class SearchHistoryItem(
    val query: String,
    val timestamp: Long,
    val location: LocationInfo? = null,
    val resultCount: Int = 0,
    val category: String? = null
)
```

### 7.3 默认实现

#### 7.3.1 DefaultSearchHistoryProvider
```kotlin
class DefaultSearchHistoryProvider(private val context: Context) : SearchHistoryProvider {
    private val prefs = context.getSharedPreferences("rider_search_history", Context.MODE_PRIVATE)
    private val gson = Gson()
    private val maxHistorySize = 50
    private val retentionDays = 30

    override fun saveSearchHistory(query: String, location: LocationInfo?, resultCount: Int) {
        val historyList = getSearchHistory().toMutableList()

        // 移除重复项
        historyList.removeAll { it.query.equals(query, ignoreCase = true) }

        // 添加新项到开头
        historyList.add(0, SearchHistoryItem(
            query = query,
            timestamp = System.currentTimeMillis(),
            location = location,
            resultCount = resultCount
        ))

        // 限制历史记录数量
        if (historyList.size > maxHistorySize) {
            historyList.removeAt(historyList.size - 1)
        }

        // 异步保存
        saveHistoryAsync(historyList)
    }

    override fun getSearchHistory(): List<SearchHistoryItem> {
        val jsonString = prefs.getString(HISTORY_KEY, null) ?: return emptyList()
        return try {
            val type = object : TypeToken<List<SearchHistoryItem>>() {}.type
            val history: List<SearchHistoryItem> = gson.fromJson(jsonString, type) ?: emptyList()

            // 过滤过期记录
            val cutoffTime = System.currentTimeMillis() - (retentionDays * 24 * 60 * 60 * 1000L)
            history.filter { it.timestamp > cutoffTime }
        } catch (e: Exception) {
            Log.e("SearchHistory", "Failed to parse search history", e)
            emptyList()
        }
    }

    private fun saveHistoryAsync(historyList: List<SearchHistoryItem>) {
        Thread {
            try {
                val jsonString = gson.toJson(historyList)
                prefs.edit().putString(HISTORY_KEY, jsonString).apply()
            } catch (e: Exception) {
                Log.e("SearchHistory", "Failed to save search history", e)
            }
        }.start()
    }

    companion object {
        private const val HISTORY_KEY = "search_history"
    }
}
```

## 8. 组合组件设计

### 8.1 RiderNavigationView - 一体化导航组件

#### 8.1.1 设计目标
为简化使用场景，提供一个集成了搜索、地图、路线、导航功能的一体化组件，开箱即用。

#### 8.1.2 组件构成
```kotlin
class RiderNavigationView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    // 内部集成的基础组件
    private val searchView: RiderSearchView
    private val mapView: RiderMapView
    private val routeView: RiderRouteView
    private val navigationView: RiderNavigationView

    // 统一回调接口
    fun setNavigationListener(listener: NavigationListener)

    interface NavigationListener {
        fun onNavigationReady()
        fun onNavigationStarted()
        fun onNavigationCompleted()
        fun onNavigationError(error: String)
    }

    // 简化的API
    fun startQuickNavigation()
    fun setDestination(query: String)
    fun enableVoiceNavigation(enabled: Boolean)
    fun updateGlobalTheme(themeName: String)
}
```

### 8.2 组件间自动交互

#### 8.2.1 自动化流程
1. **搜索完成** → 自动在地图上显示结果 → 询问是否设为目的地
2. **目的地设置** → 自动获取当前位置作为起点 → 自动计算路线
3. **路线计算完成** → 自动在地图上显示路线 → 提供导航启动选项
4. **导航启动** → 自动切换到导航界面 → 开始实时导航

#### 8.2.2 智能错误处理
- **无定位权限** → 引导用户开启定位权限
- **网络异常** → 提示检查网络连接
- **搜索无结果** → 建议修改搜索关键词
- **路线计算失败** → 建议重新选择起终点

## 9. 使用示例

### 9.1 基础组件使用

#### 9.1.1 完整使用示例
```kotlin
class MainActivity : AppCompatActivity() {

    private lateinit var mapView: RiderMapView
    private lateinit var searchView: RiderSearchView
    private lateinit var routeView: RiderRouteView
    private lateinit var navigationView: RiderNavigationView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        initViews()
        setupCallbacks()
        setupThemes()
    }

    private fun initViews() {
        mapView = findViewById(R.id.map_view)
        searchView = findViewById(R.id.search_view)
        routeView = findViewById(R.id.route_view)
        navigationView = findViewById(R.id.navigation_view)
    }

    private fun setupCallbacks() {
        // 地图组件回调
        mapView.setMapCallback(object : RiderMapView.MapCallback {
            override fun onMapClick(latitude: Double, longitude: Double) {
                handleMapClick(latitude, longitude)
            }

            override fun onMarkerClick(markerId: String) {
                handleMarkerClick(markerId)
            }
        })

        // 搜索组件回调
        searchView.setSearchCallback(object : RiderSearchView.SearchCallback {
            override fun onSearchSubmitted(query: String) {
                performSearch(query)
            }

            override fun onSearchResultItemClick(item: SearchResultItem) {
                handleSearchResultSelected(item)
            }

            override fun onHistoryItemClick(query: String) {
                performSearch(query)
            }
        })

        // 路线组件回调
        routeView.setRouteCallback(object : RiderRouteView.RouteCallback {
            override fun onStartLocationButtonClick() {
                showStartLocationOptions()
            }

            override fun onDestinationButtonClick() {
                showDestinationOptions()
            }

            override fun onCalculateRouteButtonClick() {
                calculateRoute()
            }

            override fun onStartNavigationButtonClick() {
                startNavigation()
            }

            override fun onRouteOptionClick(routeIndex: Int) {
                selectRoute(routeIndex)
            }
        })

        // 导航组件回调
        navigationView.setNavigationCallback(object : RiderNavigationView.NavigationCallback {
            override fun onStopNavigationButtonClick() {
                stopNavigation()
            }

            override fun onNavigationSettingsButtonClick() {
                openNavigationSettings()
            }

            override fun onRouteOverviewButtonClick() {
                showRouteOverview()
            }

            override fun onReportButtonClick() {
                showReportDialog()
            }
        })
    }

    private fun setupThemes() {
        // 使用构建器模式配置主题
        val nightTheme = ThemeConfigBuilder.mapTheme {
            textPrimary(Color.WHITE)
            textSecondary(Color.GRAY)
            background(Color.BLACK)
            icons {
                locationIcon(R.drawable.location_night)
                markerIcon(R.drawable.marker_night)
                zoomInIcon(R.drawable.zoom_in_night)
                zoomOutIcon(R.drawable.zoom_out_night)
            }
        }

        mapView.updateTheme(nightTheme)

        // 或者使用样式资源
        searchView.applyStyle(R.style.RiderUIKit_SearchView_Night)
    }

    // 业务逻辑处理方法
    private fun handleMapClick(latitude: Double, longitude: Double) {
        // 处理地图点击逻辑
    }

    private fun performSearch(query: String) {
        // 处理搜索逻辑
    }

    private fun calculateRoute() {
        // 处理路线计算逻辑
    }

    private fun startNavigation() {
        // 处理导航启动逻辑
    }
}
```

### 9.2 组合组件使用

#### 9.2.1 简化使用示例
```kotlin
class SimpleActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val navigationView = RiderNavigationView(this)
        setContentView(navigationView)

        // 设置监听器
        navigationView.setNavigationListener(object : RiderNavigationView.NavigationListener {
            override fun onNavigationReady() {
                // 导航准备就绪
            }

            override fun onNavigationStarted() {
                // 导航已启动
            }

            override fun onNavigationCompleted() {
                // 导航已完成
                finish()
            }

            override fun onNavigationError(error: String) {
                // 导航错误处理
                showErrorDialog(error)
            }
        })

        // 一键导航
        navigationView.startQuickNavigation()

        // 全局主题切换
        navigationView.updateGlobalTheme("night")
    }
}
```

### 9.3 XML布局使用

#### 9.3.1 XML属性配置
```xml
<!-- 使用默认主题 -->
<com.link.rideramap.ui.RiderMapView
    android:id="@+id/map_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent" />

<!-- 自定义主题属性 -->
<com.link.rideramap.ui.RiderSearchView
    android:id="@+id/search_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:searchTextPrimary="@color/custom_text_color"
    app:searchIcon="@drawable/custom_search_icon"
    app:searchBackground="@color/custom_background" />

<!-- 使用自定义样式 -->
<com.link.rideramap.ui.RiderRouteView
    android:id="@+id/route_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    style="@style/CustomRouteStyle" />
```

## 10. 对外开放API清单

### 10.1 核心UI组件API

| 组件 | API类型 | 方法/接口名称 | 参数 | 返回值 | 说明 |
|------|---------|---------------|------|--------|------|
| **RiderMapView** | 构造函数 | `RiderMapView()` | `Context, AttributeSet?, Int` | - | 地图组件构造函数 |
| | 回调设置 | `setMapCallback()` | `MapCallback` | `Unit` | 设置地图交互回调 |
| | 主题配置 | `updateTheme()` | `MapThemeConfig` | `Unit` | 更新主题配置 |
| | 主题配置 | `applyStyle()` | `@StyleRes Int` | `Unit` | 应用样式资源 |
| | 回调接口 | `MapCallback.onMapClick()` | `Double, Double` | `Unit` | 地图点击事件 |
| | 回调接口 | `MapCallback.onMarkerClick()` | `String` | `Unit` | 标记点击事件 |
| **RiderSearchView** | 构造函数 | `RiderSearchView()` | `Context, AttributeSet?, Int` | - | 搜索组件构造函数 |
| | 回调设置 | `setSearchCallback()` | `SearchCallback` | `Unit` | 设置搜索回调 |
| | 主题配置 | `updateTheme()` | `SearchThemeConfig` | `Unit` | 更新主题配置 |
| | 主题配置 | `applyStyle()` | `@StyleRes Int` | `Unit` | 应用样式资源 |
| | 回调接口 | `SearchCallback.onSearchSubmitted()` | `String` | `Unit` | 搜索提交事件 |
| | 回调接口 | `SearchCallback.onSearchResultItemClick()` | `SearchResultItem` | `Unit` | 搜索结果点击事件 |
| | 回调接口 | `SearchCallback.onHistoryItemClick()` | `String` | `Unit` | 历史项点击事件 |
| **RiderRouteView** | 构造函数 | `RiderRouteView()` | `Context, AttributeSet?, Int` | - | 路线组件构造函数 |
| | 回调设置 | `setRouteCallback()` | `RouteCallback` | `Unit` | 设置路线回调 |
| | 主题配置 | `updateTheme()` | `RouteThemeConfig` | `Unit` | 更新主题配置 |
| | 主题配置 | `applyStyle()` | `@StyleRes Int` | `Unit` | 应用样式资源 |
| | 回调接口 | `RouteCallback.onStartLocationButtonClick()` | - | `Unit` | 起点选择按钮点击 |
| | 回调接口 | `RouteCallback.onDestinationButtonClick()` | - | `Unit` | 终点选择按钮点击 |
| | 回调接口 | `RouteCallback.onCalculateRouteButtonClick()` | - | `Unit` | 计算路线按钮点击 |
| | 回调接口 | `RouteCallback.onStartNavigationButtonClick()` | - | `Unit` | 开始导航按钮点击 |
| | 回调接口 | `RouteCallback.onRouteOptionClick()` | `Int` | `Unit` | 路线选项点击 |
| **RiderNavigationView** | 构造函数 | `RiderNavigationView()` | `Context, AttributeSet?, Int` | - | 导航组件构造函数 |
| | 回调设置 | `setNavigationCallback()` | `NavigationCallback` | `Unit` | 设置导航回调 |
| | 主题配置 | `updateTheme()` | `NavigationThemeConfig` | `Unit` | 更新主题配置 |
| | 主题配置 | `applyStyle()` | `@StyleRes Int` | `Unit` | 应用样式资源 |
| | 回调接口 | `NavigationCallback.onStopNavigationButtonClick()` | - | `Unit` | 停止导航按钮点击 |
| | 回调接口 | `NavigationCallback.onNavigationSettingsButtonClick()` | - | `Unit` | 导航设置按钮点击 |
| | 回调接口 | `NavigationCallback.onRouteOverviewButtonClick()` | - | `Unit` | 路线总览按钮点击 |
| | 回调接口 | `NavigationCallback.onReportButtonClick()` | - | `Unit` | 上报按钮点击 |

### 10.2 主题配置API

| 类别 | API类型 | 方法/类名称 | 参数 | 返回值 | 说明 |
|------|---------|-------------|------|--------|------|
| **主题管理器** | 枚举 | `ThemeMode` | - | - | 主题模式枚举 |
| | 主题切换 | `RiderUIThemeManager.setTheme()` | `ThemeMode` | `Unit` | 设置主题模式 |
| | 主题切换 | `RiderUIThemeManager.setGlobalTheme()` | `String` | `Unit` | 设置全局主题 |
| | 主题切换 | `RiderUIThemeManager.getCurrentTheme()` | - | `ThemeMode` | 获取当前主题 |
| | 主题配置 | `RiderUIThemeManager.registerThemeConfig()` | `String, Map<String, Any>` | `Unit` | 注册主题配置 |
| | 主题配置 | `RiderUIThemeManager.getThemeConfig()` | `String, String` | `Any?` | 获取主题配置 |
| | 主题配置 | `RiderUIThemeManager.initializeDefaultThemes()` | `Context` | `Unit` | 初始化默认主题 |
| | 监听器 | `RiderUIThemeManager.registerThemeListener()` | `ThemeChangeListener` | `Unit` | 注册主题监听器 |
| | 监听器 | `RiderUIThemeManager.unregisterThemeListener()` | `ThemeChangeListener` | `Unit` | 注销主题监听器 |
| | 监听器接口 | `ThemeChangeListener.onThemeChanged()` | `Boolean` | `Unit` | 主题变化回调 |
| **主题构建器** | 构建器 | `ThemeConfigBuilder.mapTheme()` | `MapThemeBuilder.() -> Unit` | `MapThemeConfig` | 构建地图主题 |
| | 构建器 | `ThemeConfigBuilder.searchTheme()` | `SearchThemeBuilder.() -> Unit` | `SearchThemeConfig` | 构建搜索主题 |
| | 构建器 | `ThemeConfigBuilder.routeTheme()` | `RouteThemeBuilder.() -> Unit` | `RouteThemeConfig` | 构建路线主题 |
| | 构建器 | `ThemeConfigBuilder.navigationTheme()` | `NavigationThemeBuilder.() -> Unit` | `NavigationThemeConfig` | 构建导航主题 |

### 10.3 数据模型API

| 类别 | 数据类名称 | 属性 | 类型 | 说明 |
|------|------------|------|------|------|
| **位置信息** | `LocationInfo` | `latitude` | `Double` | 纬度 |
| | | `longitude` | `Double` | 经度 |
| | | `address` | `String` | 地址 |
| | | `accuracy` | `Float` | 精度 |
| | | `timestamp` | `Long` | 时间戳 |
| **搜索结果** | `SearchResultItem` | `id` | `String` | 结果ID |
| | | `name` | `String` | 名称 |
| | | `address` | `String` | 地址 |
| | | `location` | `LocationInfo` | 位置信息 |
| | | `category` | `String` | 分类 |
| | | `distance` | `Float` | 距离 |
| | | `rating` | `Float` | 评分 |
| **路线信息** | `RouteInfo` | `routeId` | `String` | 路线ID |
| | | `startLocation` | `LocationInfo` | 起点 |
| | | `endLocation` | `LocationInfo` | 终点 |
| | | `distance` | `Float` | 距离 |
| | | `duration` | `Int` | 时长 |
| | | `strategy` | `RouteStrategy` | 路线策略 |
| | | `polylinePoints` | `List<LocationInfo>` | 路线点集合 |

### 10.4 主题配置数据类API

| 组件 | 配置类名称 | 属性 | 类型 | 说明 |
|------|------------|------|------|------|
| **地图组件** | `MapThemeConfig` | `textPrimary` | `Int?` | 主要文字颜色 |
| | | `textSecondary` | `Int?` | 次要文字颜色 |
| | | `background` | `Int?` | 背景颜色 |
| | | `icons` | `MapIconConfig?` | 图标配置 |
| | `MapIconConfig` | `locationIcon` | `Int?` | 定位图标 |
| | | `markerIcon` | `Int?` | 标记图标 |
| | | `zoomInIcon` | `Int?` | 放大图标 |
| | | `zoomOutIcon` | `Int?` | 缩小图标 |
| **搜索组件** | `SearchThemeConfig` | `textPrimary` | `Int?` | 主要文字颜色 |
| | | `textHint` | `Int?` | 提示文字颜色 |
| | | `background` | `Int?` | 背景颜色 |
| | | `itemBackground` | `Int?` | 列表项背景颜色 |
| | | `icons` | `SearchIconConfig?` | 图标配置 |
| | `SearchIconConfig` | `searchIcon` | `Int?` | 搜索图标 |
| | | `clearIcon` | `Int?` | 清空图标 |
| | | `voiceIcon` | `Int?` | 语音图标 |
| | | `historyIcon` | `Int?` | 历史图标 |
| **路线组件** | `RouteThemeConfig` | `textPrimary` | `Int?` | 主要文字颜色 |
| | | `textSecondary` | `Int?` | 次要文字颜色 |
| | | `buttonBackground` | `Int?` | 按钮背景颜色 |
| | | `buttonText` | `Int?` | 按钮文字颜色 |
| | | `icons` | `RouteIconConfig?` | 图标配置 |
| | `RouteIconConfig` | `startIcon` | `Int?` | 起点图标 |
| | | `endIcon` | `Int?` | 终点图标 |
| | | `swapIcon` | `Int?` | 交换图标 |
| | | `calculateIcon` | `Int?` | 计算图标 |
| **导航组件** | `NavigationThemeConfig` | `textPrimary` | `Int?` | 主要文字颜色 |
| | | `textSecondary` | `Int?` | 次要文字颜色 |
| | | `background` | `Int?` | 背景颜色 |
| | | `accent` | `Int?` | 强调色 |
| | | `icons` | `NavigationIconConfig?` | 图标配置 |
| | `NavigationIconConfig` | `stopIcon` | `Int?` | 停止图标 |
| | | `settingsIcon` | `Int?` | 设置图标 |
| | | `overviewIcon` | `Int?` | 总览图标 |
| | | `reportIcon` | `Int?` | 上报图标 |

### 10.5 搜索历史API

| 类别 | API类型 | 方法/接口名称 | 参数 | 返回值 | 说明 |
|------|---------|---------------|------|--------|------|
| **历史提供者** | 接口 | `SearchHistoryProvider` | - | - | 搜索历史提供者接口 |
| | 保存 | `saveSearchHistory()` | `String, LocationInfo?, Int` | `Unit` | 保存搜索历史 |
| | 获取 | `getSearchHistory()` | - | `List<SearchHistoryItem>` | 获取搜索历史 |
| | 建议 | `getSearchSuggestions()` | `String` | `List<String>` | 获取搜索建议 |
| | 清除 | `clearSearchHistory()` | - | `Unit` | 清除搜索历史 |
| | 删除 | `removeSearchItem()` | `String` | `Unit` | 删除搜索项 |
| | 热门 | `getPopularSearches()` | `Int` | `List<String>` | 获取热门搜索 |
| | 导出 | `exportHistory()` | - | `String` | 导出历史数据 |
| | 导入 | `importHistory()` | `String` | `Boolean` | 导入历史数据 |
| **默认实现** | 实现类 | `DefaultSearchHistoryProvider` | `Context` | - | 默认历史提供者 |

### 10.6 组合组件API

| 组件 | API类型 | 方法/接口名称 | 参数 | 返回值 | 说明 |
|------|---------|---------------|------|--------|------|
| **一体化导航** | 构造函数 | `RiderNavigationView()` | `Context, AttributeSet?, Int` | - | 一体化导航组件 |
| | 回调设置 | `setNavigationListener()` | `NavigationListener` | `Unit` | 设置导航监听器 |
| | 快速导航 | `startQuickNavigation()` | - | `Unit` | 启动快速导航 |
| | 设置目的地 | `setDestination()` | `String` | `Unit` | 设置目的地 |
| | 语音导航 | `enableVoiceNavigation()` | `Boolean` | `Unit` | 启用语音导航 |
| | 全局主题 | `updateGlobalTheme()` | `String` | `Unit` | 更新全局主题 |
| | 监听器接口 | `NavigationListener.onNavigationReady()` | - | `Unit` | 导航准备就绪 |
| | 监听器接口 | `NavigationListener.onNavigationStarted()` | - | `Unit` | 导航已启动 |
| | 监听器接口 | `NavigationListener.onNavigationCompleted()` | - | `Unit` | 导航已完成 |
| | 监听器接口 | `NavigationListener.onNavigationError()` | `String` | `Unit` | 导航错误 |

### 10.7 XML自定义属性API

| 组件 | 属性名称 | 格式 | 说明 |
|------|----------|------|------|
| **RiderMapView** | `mapTextPrimary` | `color\|reference` | 地图主要文字颜色 |
| | `mapTextSecondary` | `color\|reference` | 地图次要文字颜色 |
| | `mapBackground` | `color\|reference` | 地图背景颜色 |
| | `mapLocationIcon` | `reference` | 定位图标 |
| | `mapMarkerIcon` | `reference` | 标记图标 |
| | `mapZoomInIcon` | `reference` | 放大图标 |
| | `mapZoomOutIcon` | `reference` | 缩小图标 |
| **RiderSearchView** | `searchTextPrimary` | `color\|reference` | 搜索主要文字颜色 |
| | `searchTextHint` | `color\|reference` | 搜索提示文字颜色 |
| | `searchBackground` | `color\|reference` | 搜索背景颜色 |
| | `searchItemBackground` | `color\|reference` | 搜索项背景颜色 |
| | `searchIcon` | `reference` | 搜索图标 |
| | `searchClearIcon` | `reference` | 清空图标 |
| | `searchVoiceIcon` | `reference` | 语音图标 |
| | `searchHistoryIcon` | `reference` | 历史图标 |
| **RiderRouteView** | `routeTextPrimary` | `color\|reference` | 路线主要文字颜色 |
| | `routeTextSecondary` | `color\|reference` | 路线次要文字颜色 |
| | `routeButtonBackground` | `color\|reference` | 路线按钮背景颜色 |
| | `routeButtonText` | `color\|reference` | 路线按钮文字颜色 |
| | `routeStartIcon` | `reference` | 起点图标 |
| | `routeEndIcon` | `reference` | 终点图标 |
| | `routeSwapIcon` | `reference` | 交换图标 |
| | `routeCalculateIcon` | `reference` | 计算图标 |
| **RiderNavigationView** | `navigationTextPrimary` | `color\|reference` | 导航主要文字颜色 |
| | `navigationTextSecondary` | `color\|reference` | 导航次要文字颜色 |
| | `navigationBackground` | `color\|reference` | 导航背景颜色 |
| | `navigationAccent` | `color\|reference` | 导航强调色 |
| | `navigationStopIcon` | `reference` | 停止图标 |
| | `navigationSettingsIcon` | `reference` | 设置图标 |
| | `navigationOverviewIcon` | `reference` | 总览图标 |
| | `navigationReportIcon` | `reference` | 上报图标 |
