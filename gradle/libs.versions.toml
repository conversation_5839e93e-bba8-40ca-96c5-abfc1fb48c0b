[versions]
agp = "8.6.1"
appcompat = "1.6.1"
core-ktx = "1.7.0"
kotlin = "1.8.0"
dokka = "1.7.20"


google-material = "1.8.0"

amap = "9.8.2_3dmap9.8.2"
amap-search = "9.7.0"

junit = "4.13.2"
androidx-test-ext = "1.1.5"
espresso-core = "3.5.1"

[libraries]
amap = {module= "com.amap.api:navi-3dmap", version.ref = "amap"}
amap-search = {module = "com.amap.api:search", version.ref = "amap-search"}
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "core-ktx" }
google-material = {module = "com.google.android.material:material", version.ref = "google-material"}

junit = {module = "junit:junit", version.ref = "junit"}
androidx-test-ext = {module = "androidx.test.ext:junit", version.ref = "androidx-test-ext"}
espresso-core = {module = "androidx.test.espresso:espresso-core", version.ref = "espresso-core"}

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
jetbrains-dokka = { id = "org.jetbrains.dokka", version.ref = "dokka" }
