<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00000000">

    <!-- 背景视图 -->
    <View
        android:id="@+id/search_dialog_background_img"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY" />

    <!-- 搜索框容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/search_box"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginStart="5dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/search_dialog_background_img">

        <!-- 搜索框背景 -->
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:scaleType="fitXY"
            android:src="@drawable/search_tv_background"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/ib_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="7dp"
            android:layout_marginTop="4dp"
            android:src="@drawable/back_search"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 搜索输入框 -->
        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/tv_address_name"
            android:layout_width="0dp"
            android:layout_height="18dp"
            android:layout_marginStart="53dp"
            android:layout_marginTop="9dp"
            android:layout_marginEnd="36dp"
            android:background="@null"
            android:hint="@string/dialog_search_et_hint"
            android:imeOptions="actionSearch"
            android:inputType="text"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="#202229"
            android:textSize="13sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 取消按钮 -->
        <ImageView
            android:id="@+id/search_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/cancel_search"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 内容切换器 -->
    <ViewAnimator
        android:id="@+id/view_animator"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="60dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/search_box">

        <!-- 历史记录页面 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/history_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="true"
                android:scrollbars="vertical"
                android:visibility="gone"
                android:overScrollMode="ifContentScrolls"
                android:scrollbarStyle="insideOverlay" />

            <!-- 历史记录为空时的提示 -->
            <TextView
                android:id="@+id/tv_history_empty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="暂无搜索历史"
                android:textColor="#999999"
                android:textSize="14sp"
                android:visibility="gone" />
        </FrameLayout>

        <!-- 搜索结果页面 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/search_result_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="true"
                android:scrollbarAlwaysDrawVerticalTrack="true"
                android:scrollbarStyle="insideOverlay"
                android:scrollbars="vertical"
                android:overScrollMode="ifContentScrolls" />

            <!-- 搜索结果为空时的提示 -->
            <TextView
                android:id="@+id/tv_search_empty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="未找到相关结果"
                android:textColor="#999999"
                android:textSize="14sp"
                android:visibility="gone" />

            <!-- 搜索加载中提示 -->
            <LinearLayout
                android:id="@+id/ll_search_loading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="gone">

                <ProgressBar
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="搜索中..."
                    android:textColor="#999999"
                    android:textSize="14sp" />
            </LinearLayout>
        </FrameLayout>
    </ViewAnimator>

</androidx.constraintlayout.widget.ConstraintLayout>
