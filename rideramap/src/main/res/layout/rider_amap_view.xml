<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <com.amap.api.maps.TextureMapView
        android:id="@+id/map"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_locate"
            android:src="@drawable/mylocation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            app:layout_constraintBottom_toTopOf="@+id/linearLayoutCompat"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_traffic"
            android:src="@drawable/btn_road_condition_on"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="79dp"
            android:layout_marginEnd="17dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/linearLayoutCompat"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@drawable/search_background"
            android:layout_marginBottom="54dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_address_name"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="31dp"
                android:layout_weight="1"
                android:hint="@string/ramap_search_hint"
                android:textColorHint="@color/ramap_search_hint_text_color"
                android:gravity="center_vertical"
                android:textColor="@color/ramap_search_text_color"
                android:textSize="14sp" />

            <ImageButton
                android:id="@+id/ib_search"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="23dp"
                android:background="@drawable/btn_map_search" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/v_map_shadow"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</FrameLayout>