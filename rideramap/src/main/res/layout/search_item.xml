<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dl_search_item"
    android:layout_width="match_parent"
    android:layout_height="50dp">

    <ImageView
        android:id="@+id/iv_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/item_location"
        android:layout_marginTop="15dp"
        android:layout_marginStart="12dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_address"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        android:maxLength="30"
        android:text="@string/address"
        android:textSize="13sp"
        android:layout_marginStart="48dp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <ImageView
        android:id="@+id/img_go_route"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/item_route"
        android:layout_marginEnd="20dp"
        android:layout_marginTop="15dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E7E9EF"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>