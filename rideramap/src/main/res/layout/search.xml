<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#00000000">
    <View
        android:id="@+id/search_dialog_background_img"
        android:scaleType="fitXY"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/search_box"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginEnd="20dp"
        android:layout_marginStart="5dp"
        android:layout_marginTop="12dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/search_dialog_background_img">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:scaleType="fitXY"
            android:src="@drawable/search_tv_background"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>
        <ImageView
            android:id="@+id/ib_back"
            android:src="@drawable/back_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="7dp"
            android:layout_marginTop="4dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <androidx.appcompat.widget.AppCompatEditText
            android:background="@null"
            android:id="@+id/tv_address_name"
            android:layout_width="match_parent"
            android:layout_height="18dp"
            android:layout_marginStart="53dp"
            android:layout_marginTop="9dp"
            android:layout_marginEnd="36dp"
            android:imeOptions="actionSearch"
            android:hint="@string/dialog_search_et_hint"
            android:textSize="13sp"
            android:textColor="#202229"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <ImageView
            android:id="@+id/search_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/cancel_search"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <ViewAnimator
        android:id="@+id/view_animator"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="60dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toBottomOf="@+id/search_box">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/history_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="true"
                android:scrollbars="vertical"
                android:visibility="gone" />
        </FrameLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/search_result_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="true"
            android:scrollbarAlwaysDrawVerticalTrack="true"
            android:scrollbarStyle="insideOverlay"
            android:scrollbars="vertical" />
    </ViewAnimator>

</androidx.constraintlayout.widget.ConstraintLayout>