<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.amap.api.maps.TextureMapView
        android:id="@+id/map"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/linearLayoutCompat"
                android:layout_width="match_parent"
                android:layout_height="76dp"
                android:background="#FFFFFF"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                >

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/ll_back"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="4dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <ImageView
                        android:id="@+id/ib_back"
                        android:layout_width="8dp"
                        android:layout_height="12dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="8dp"
                        android:background="@drawable/btn_map_back"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.appcompat.widget.LinearLayoutCompat>
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/route_top_box"
                    android:layout_width="match_parent"
                    android:layout_height="66dp"
                    android:layout_marginStart="48dp"
                    android:layout_marginEnd="20dp"
                    android:background="#FFFFFF"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <ImageView
                        android:id="@+id/se_background"
                        android:background="@drawable/start_end_background"
                        android:layout_width="match_parent"
                        android:layout_height="66dp"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_address_start"
                        android:layout_width="match_parent"
                        android:layout_height="18dp"
                        android:text="起点"
                        android:layout_marginTop="9dp"
                        android:layout_marginStart="26dp"
                        android:layout_marginEnd="5dp"
                        android:maxLines="1"
                        android:textColor="#323232"
                        android:textSize="13sp"
                        app:layout_constraintLeft_toLeftOf="@+id/imageView"
                        app:layout_constraintRight_toRightOf="@+id/se_background"
                        app:layout_constraintTop_toTopOf="@+id/se_background" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_address_end"
                        android:layout_width="match_parent"
                        android:layout_height="18dp"
                        android:text="终点"
                        android:layout_marginTop="39dp"
                        android:layout_marginStart="26dp"
                        android:layout_marginEnd="5dp"
                        android:maxLines="1"
                        android:textColor="#323232"
                        android:textSize="13sp"
                        app:layout_constraintBottom_toBottomOf="@+id/imageView4"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/se_background" />

                    <ImageView
                        android:id="@+id/imageView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_marginTop="15dp"
                        app:layout_constraintLeft_toLeftOf="@+id/se_background"
                        app:layout_constraintTop_toTopOf="@+id/se_background"
                        app:srcCompat="@drawable/start_point" />

                    <ImageView
                        android:id="@+id/imageView3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="27dp"
                        android:layout_marginStart="14dp"
                        app:layout_constraintLeft_toLeftOf="@+id/se_background"
                        app:layout_constraintTop_toTopOf="@+id/se_background"
                        app:srcCompat="@drawable/five_point" />

                    <ImageView
                        android:id="@+id/imageView4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="44dp"
                        android:layout_marginStart="12dp"
                        app:layout_constraintLeft_toLeftOf="@+id/se_background"
                        app:layout_constraintTop_toTopOf="@+id/se_background"
                        app:srcCompat="@drawable/end" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>



            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout3"
                android:layout_width="match_parent"
                android:layout_height="167dp"
                android:background="#00000000"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">
                <ImageView
                    android:id="@+id/select_route_background"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/route_select_background"/>


                <LinearLayout
                    android:id="@+id/route_bottom_box"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/route_select_background"
                    app:layout_constraintTop_toTopOf="parent"
                    android:orientation="horizontal">
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_weight="1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.link.rideramap.presentation.component.route.RouteButtonView
                            android:id="@+id/first_route"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:ignore="InOrMmUsage" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_weight="1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.link.rideramap.presentation.component.route.RouteButtonView
                            android:id="@+id/second_route"
                            android:visibility="gone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:ignore="InOrMmUsage" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_weight="1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.link.rideramap.presentation.component.route.RouteButtonView
                            android:id="@+id/third_route"
                            android:visibility="gone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:ignore="InOrMmUsage" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>
                <Button
                    android:id="@+id/btn_navigate"
                    android:enabled="false"
                    android:layout_width="104dp"
                    android:layout_height="36dp"
                    android:layout_marginEnd="20dp"
                    android:layout_marginBottom="20dp"
                    android:background="@drawable/bt_map_navi_background"
                    android:text="开始导航"
                    android:textColor="#5C7BD7"
                    android:textStyle="bold"
                    android:textSize="13sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/cover_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <include
            android:id="@+id/cover_layout_new"
            layout="@layout/fragment_cover_navi" />
    </FrameLayout>
</FrameLayout>