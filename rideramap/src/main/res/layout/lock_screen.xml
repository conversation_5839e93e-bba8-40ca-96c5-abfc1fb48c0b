<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/virtual_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <TextView
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:gravity="center"
        android:text="@string/lock_screen_content"
        android:textColor="#ffffff"
        android:textSize="32sp" />
</RelativeLayout>