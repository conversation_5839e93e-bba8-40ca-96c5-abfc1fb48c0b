<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/route_cl"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00000000">
    <TextView
        android:id="@+id/title_tv"
        android:layout_width="wrap_content"
        android:layout_height="17dp"
        android:text="标题"
        android:textSize="12sp"
        android:layout_marginTop="12dp"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>
    <TextView
        android:id="@+id/total_minutes_tv"
        android:layout_width="wrap_content"
        android:layout_height="22dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="35dp"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:text="0 分钟"
        android:textSize="16sp"
        app:layout_constraintBottom_toTopOf="@+id/traffic_light_num_tv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <ImageView
        android:id="@+id/traffic_light_num_img"
        android:src="@drawable/traffic_light_num_idle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="2dp"
        app:layout_constraintTop_toTopOf="@+id/distance_tv"
        app:layout_constraintBottom_toBottomOf="@+id/distance_tv"
        app:layout_constraintRight_toLeftOf="@+id/traffic_light_num_tv" />
    <TextView
        android:id="@+id/traffic_light_num_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="0"
        android:textSize="11sp"
        android:layout_marginStart="24dp"
        app:layout_constraintTop_toTopOf="@+id/distance_tv"
        app:layout_constraintLeft_toRightOf="@+id/distance_tv" />

    <TextView
        android:id="@+id/distance_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="0 km"
        android:textSize="11sp"
        android:textStyle="bold"
        android:layout_marginBottom="2dp"
        app:layout_constraintBottom_toTopOf="@+id/time_tv"
        app:layout_constraintLeft_toLeftOf="@+id/time_tv" />

    <TextView
        android:id="@+id/time_tv"
        android:layout_width="wrap_content"
        android:layout_height="15dp"
        android:layout_marginEnd="3dp"
        android:includeFontPadding="false"
        android:text="预计10.23到达"
        android:textSize="11sp"
        android:layout_marginBottom="12dp"
        android:layout_marginStart="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
