<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#F0F0F0"
    android:clickable="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true">


    <ImageView
        android:layout_marginTop="125dp"
        android:id="@+id/iv_cover_logo"
        android:layout_width="201dp"
        android:layout_height="110dp"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/navi_cycling"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_navi_type"
        android:layout_marginTop="15dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text="开始导航"
        android:textColor="#323232"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_cover_logo" />

    <Button
        android:id="@+id/btn_stop_navi"
        android:layout_marginTop="109dp"
        android:layout_width="0dp"
        android:layout_height="53dp"
        android:layout_marginStart="19dp"
        android:layout_marginEnd="19dp"
        android:background="@drawable/bt_cancel_btn_selector"
        android:text="取消"
        android:textColor="@color/cancel_btn_text_color_selector"
        android:textSize="20sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_navi_type" />


</androidx.constraintlayout.widget.ConstraintLayout>
