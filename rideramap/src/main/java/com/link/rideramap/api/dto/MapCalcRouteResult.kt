package com.link.rideramap.api.dto

import com.link.rideramap.core.search.domain.entity.RouteData

/**
 * 路径计算结果类
 * @property errorCode 错误代码，1000代表成功
 * @property errorDetail 错误详细信息
 * @property errorDescription 错误描述
 * @property routeList 路径列表
 * @see RouteData
 */
data class MapCalcRouteResult(
    val errorCode: Int,
    val errorDetail: String,
    val errorDescription: String?,
    val routeList: List<RouteData>
)
