package com.link.rideramap.api.callback

import com.link.rideramap.api.dto.MapCalcRouteResult

/**
 * <AUTHOR>
 * @date 2022/6/29
 */

interface PlanRoutesListener {
    /**
     * 计算路径成功回调 [routeResult]
     * @see com.link.rideramap.api.dto.MapCalcRouteResult
     */
    fun onCalculateRouteSuccess(routeResult: MapCalcRouteResult)

    /**
     * 计算路径失败回调 [routeResult]
     * @see MapCalcRouteResult
     */
    fun onCalculateRouteFailure(routeResult: MapCalcRouteResult)
}