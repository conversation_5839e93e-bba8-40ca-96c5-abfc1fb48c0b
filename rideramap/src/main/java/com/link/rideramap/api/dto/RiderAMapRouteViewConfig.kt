package com.link.rideramap.api.dto

import android.graphics.drawable.Drawable
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes

/**
 * 路线组件主题配置数据类
 *
 * 提供路线组件的完整主题配置，采用统一的颜色主题系统：
 * - 主题颜色：用于选中状态、强调元素
 * - 主要文字颜色：用于重要文字内容
 * - 次要文字颜色：用于辅助文字内容
 * - 背景和图标资源配置
 */
data class RiderAMapRouteViewConfig(
    // ========== 统一颜色主题 ==========
    /**
     * 主题色 - 用于选中状态、强调元素
     * 应用于：导航按钮文字、选中路线按钮文字、选中路线颜色等
     */
    @ColorInt val primaryColor: Int? = null,

    /**
     * 主要文字颜色 - 用于重要文字内容
     * 应用于：导航时间、导航距离等重要信息
     */
    @ColorInt val primaryTextColor: Int? = null,

    /**
     * 次要文字颜色 - 用于辅助文字内容
     * 应用于：未选中路线按钮文字、路线时间距离等辅助信息
     */
    @ColorInt val secondaryTextColor: Int? = null,

    // ========== 背景配置 ==========
    @DrawableRes val routeBackgroundRes: Int? = null,
    val routeBackgroundDrawable: Drawable? = null,

    @DrawableRes val routeSelectBackgroundRes: Int? = null,
    val routeSelectBackgroundDrawable: Drawable? = null,

    @DrawableRes val startEndBackgroundRes: Int? = null,
    val startEndBackgroundDrawable: Drawable? = null,

    // ========== 导航按钮配置 ==========
    @DrawableRes val naviButtonBackgroundRes: Int? = null,
    val naviButtonBackgroundDrawable: Drawable? = null,
    val naviButtonText: String? = null,

    // ========== 返回按钮配置 ==========
    @DrawableRes val backButtonIconRes: Int? = null,
    val backButtonIconDrawable: Drawable? = null,

    // ========== RouteButtonView 配置 ==========
    @DrawableRes val routeButtonSelectedBackgroundRes: Int? = null,
    val routeButtonSelectedBackgroundDrawable: Drawable? = null,

    @DrawableRes val routeButtonUnselectedBackgroundRes: Int? = null,
    val routeButtonUnselectedBackgroundDrawable: Drawable? = null,

    @DrawableRes val routeButtonTrafficLightSelectedIconRes: Int? = null,
    val routeButtonTrafficLightSelectedIconDrawable: Drawable? = null,

    @DrawableRes val routeButtonTrafficLightUnselectedIconRes: Int? = null,
    val routeButtonTrafficLightUnselectedIconDrawable: Drawable? = null,

    // ========== 向后兼容的具体颜色配置 ==========
    // 如果设置了这些具体颜色，会覆盖统一主题颜色
    @ColorInt val naviButtonTextColor: Int? = null,
    @ColorInt val routeButtonSelectedTextColor: Int? = null,
    @ColorInt val routeButtonUnselectedTextColor: Int? = null,
    @ColorInt val routeTimeTextColor: Int? = null,
    @ColorInt val routeDistanceTextColor: Int? = null,
    @ColorInt val routeSelectedColor: Int? = null,
    @ColorInt val naviTimeTextColor: Int? = null,
    @ColorInt val naviDistanceTextColor: Int? = null,
    
    // 导航相关图标配置
    @DrawableRes val trafficOnIconRes: Int? = null,
    val trafficOnIconDrawable: Drawable? = null,
    
    @DrawableRes val trafficOffIconRes: Int? = null,
    val trafficOffIconDrawable: Drawable? = null,
    
    @DrawableRes val mapTypeAutoIconRes: Int? = null,
    val mapTypeAutoIconDrawable: Drawable? = null,
    
    @DrawableRes val mapTypeDayIconRes: Int? = null,
    val mapTypeDayIconDrawable: Drawable? = null,
    
    @DrawableRes val mapTypeNightIconRes: Int? = null,
    val mapTypeNightIconDrawable: Drawable? = null,
    
    @DrawableRes val overviewModeIconRes: Int? = null,
    val overviewModeIconDrawable: Drawable? = null,
    
    @DrawableRes val lockModeIconRes: Int? = null,
    val lockModeIconDrawable: Drawable? = null,
    
    @DrawableRes val exitNaviIconRes: Int? = null,
    val exitNaviIconDrawable: Drawable? = null,
    
    // 导航UI背景配置
    @DrawableRes val naviRightBarBackgroundRes: Int? = null,
    val naviRightBarBackgroundDrawable: Drawable? = null,
    
    @DrawableRes val naviBottomBarBackgroundRes: Int? = null,
    val naviBottomBarBackgroundDrawable: Drawable? = null,

    // 地图阴影配置
    @ColorInt val mapShadowColor: Int? = null,

    // 地图类型配置
    val mapType: Int? = null
) {

    // ========== 颜色获取便利方法 ==========

    /**
     * 获取导航按钮文字颜色
     * 优先级：具体配置 > 主题色
     */
    fun resolveNaviButtonTextColor(): Int? = naviButtonTextColor ?: primaryColor

    /**
     * 获取路线按钮选中文字颜色
     * 优先级：具体配置 > 主题色
     */
    fun resolveRouteButtonSelectedTextColor(): Int? = routeButtonSelectedTextColor ?: primaryColor

    /**
     * 获取路线按钮未选中文字颜色
     * 优先级：具体配置 > 次要文字颜色
     */
    fun resolveRouteButtonUnselectedTextColor(): Int? = routeButtonUnselectedTextColor ?: secondaryTextColor

    /**
     * 获取路线时间文字颜色
     * 优先级：具体配置 > 次要文字颜色
     */
    fun resolveRouteTimeTextColor(): Int? = routeTimeTextColor ?: secondaryTextColor

    /**
     * 获取路线距离文字颜色
     * 优先级：具体配置 > 次要文字颜色
     */
    fun resolveRouteDistanceTextColor(): Int? = routeDistanceTextColor ?: secondaryTextColor

    /**
     * 获取选中路线颜色
     * 优先级：具体配置 > 主题色
     */
    fun resolveRouteSelectedColor(): Int? = routeSelectedColor ?: primaryColor

    /**
     * 获取导航时间文字颜色
     * 优先级：具体配置 > 主要文字颜色
     */
    fun resolveNaviTimeTextColor(): Int? = naviTimeTextColor ?: primaryTextColor

    /**
     * 获取导航距离文字颜色
     * 优先级：具体配置 > 主要文字颜色
     */
    fun resolveNaviDistanceTextColor(): Int? = naviDistanceTextColor ?: primaryTextColor
    companion object {
        /**
         * 创建默认配置
         */
        fun createDefault(): RiderAMapRouteViewConfig {
            return RiderAMapRouteViewConfig()
        }

        /**
         * 创建日间主题
         * @param brandColor 品牌主色，默认为蓝色
         */
        fun createDayTheme(brandColor: Int = 0xFF5C7BD7.toInt()): RiderAMapRouteViewConfig {
            return RiderAMapRouteViewConfig(
                primaryColor = brandColor,
                primaryTextColor = 0xFF191919.toInt(), // 深色文字
                secondaryTextColor = 0xFF666666.toInt(), // 灰色文字
                mapType = 1 // AMap.MAP_TYPE_NORMAL
            )
        }

        /**
         * 创建夜间主题
         * @param brandColor 品牌主色，默认为白色
         */
        fun createNightTheme(brandColor: Int = 0xFFFFFFFF.toInt()): RiderAMapRouteViewConfig {
            return RiderAMapRouteViewConfig(
                primaryColor = brandColor,
                primaryTextColor = 0xFFFFFFFF.toInt(), // 白色文字
                secondaryTextColor = 0xFFCCCCCC.toInt(), // 浅灰色文字
                mapType = 4 // AMap.MAP_TYPE_NIGHT
            )
        }

        /**
         * 创建品牌主题
         * @param brandColor 品牌主色
         * @param isNightMode 是否夜间模式
         */
        fun createBrandTheme(brandColor: Int, isNightMode: Boolean = false): RiderAMapRouteViewConfig {
            return if (isNightMode) {
                createNightTheme(brandColor)
            } else {
                createDayTheme(brandColor)
            }
        }

        /**
         * 合并两个配置，primary 优先于 secondary
         */
        fun merge(primary: RiderAMapRouteViewConfig?, secondary: RiderAMapRouteViewConfig?): RiderAMapRouteViewConfig {
            if (primary == null) return secondary ?: createDefault()
            if (secondary == null) return primary

            return RiderAMapRouteViewConfig(
                // 统一颜色主题
                primaryColor = primary.primaryColor ?: secondary.primaryColor,
                primaryTextColor = primary.primaryTextColor ?: secondary.primaryTextColor,
                secondaryTextColor = primary.secondaryTextColor ?: secondary.secondaryTextColor,

                // 背景配置
                routeBackgroundRes = primary.routeBackgroundRes ?: secondary.routeBackgroundRes,
                routeBackgroundDrawable = primary.routeBackgroundDrawable ?: secondary.routeBackgroundDrawable,
                routeSelectBackgroundRes = primary.routeSelectBackgroundRes ?: secondary.routeSelectBackgroundRes,
                routeSelectBackgroundDrawable = primary.routeSelectBackgroundDrawable ?: secondary.routeSelectBackgroundDrawable,
                startEndBackgroundRes = primary.startEndBackgroundRes ?: secondary.startEndBackgroundRes,
                startEndBackgroundDrawable = primary.startEndBackgroundDrawable ?: secondary.startEndBackgroundDrawable,

                // 导航按钮配置
                naviButtonBackgroundRes = primary.naviButtonBackgroundRes ?: secondary.naviButtonBackgroundRes,
                naviButtonBackgroundDrawable = primary.naviButtonBackgroundDrawable ?: secondary.naviButtonBackgroundDrawable,
                naviButtonText = primary.naviButtonText ?: secondary.naviButtonText,

                // 返回按钮配置
                backButtonIconRes = primary.backButtonIconRes ?: secondary.backButtonIconRes,
                backButtonIconDrawable = primary.backButtonIconDrawable ?: secondary.backButtonIconDrawable,

                // RouteButtonView 配置
                routeButtonSelectedBackgroundRes = primary.routeButtonSelectedBackgroundRes ?: secondary.routeButtonSelectedBackgroundRes,
                routeButtonSelectedBackgroundDrawable = primary.routeButtonSelectedBackgroundDrawable ?: secondary.routeButtonSelectedBackgroundDrawable,
                routeButtonUnselectedBackgroundRes = primary.routeButtonUnselectedBackgroundRes ?: secondary.routeButtonUnselectedBackgroundRes,
                routeButtonUnselectedBackgroundDrawable = primary.routeButtonUnselectedBackgroundDrawable ?: secondary.routeButtonUnselectedBackgroundDrawable,
                routeButtonTrafficLightSelectedIconRes = primary.routeButtonTrafficLightSelectedIconRes ?: secondary.routeButtonTrafficLightSelectedIconRes,
                routeButtonTrafficLightSelectedIconDrawable = primary.routeButtonTrafficLightSelectedIconDrawable ?: secondary.routeButtonTrafficLightSelectedIconDrawable,
                routeButtonTrafficLightUnselectedIconRes = primary.routeButtonTrafficLightUnselectedIconRes ?: secondary.routeButtonTrafficLightUnselectedIconRes,
                routeButtonTrafficLightUnselectedIconDrawable = primary.routeButtonTrafficLightUnselectedIconDrawable ?: secondary.routeButtonTrafficLightUnselectedIconDrawable,

                // 向后兼容的具体颜色配置
                naviButtonTextColor = primary.naviButtonTextColor ?: secondary.naviButtonTextColor,
                routeButtonSelectedTextColor = primary.routeButtonSelectedTextColor ?: secondary.routeButtonSelectedTextColor,
                routeButtonUnselectedTextColor = primary.routeButtonUnselectedTextColor ?: secondary.routeButtonUnselectedTextColor,
                routeTimeTextColor = primary.routeTimeTextColor ?: secondary.routeTimeTextColor,
                routeDistanceTextColor = primary.routeDistanceTextColor ?: secondary.routeDistanceTextColor,
                routeSelectedColor = primary.routeSelectedColor ?: secondary.routeSelectedColor,
                naviTimeTextColor = primary.naviTimeTextColor ?: secondary.naviTimeTextColor,
                naviDistanceTextColor = primary.naviDistanceTextColor ?: secondary.naviDistanceTextColor,
                trafficOnIconRes = primary.trafficOnIconRes ?: secondary.trafficOnIconRes,
                trafficOnIconDrawable = primary.trafficOnIconDrawable ?: secondary.trafficOnIconDrawable,
                trafficOffIconRes = primary.trafficOffIconRes ?: secondary.trafficOffIconRes,
                trafficOffIconDrawable = primary.trafficOffIconDrawable ?: secondary.trafficOffIconDrawable,
                mapTypeAutoIconRes = primary.mapTypeAutoIconRes ?: secondary.mapTypeAutoIconRes,
                mapTypeAutoIconDrawable = primary.mapTypeAutoIconDrawable ?: secondary.mapTypeAutoIconDrawable,
                mapTypeDayIconRes = primary.mapTypeDayIconRes ?: secondary.mapTypeDayIconRes,
                mapTypeDayIconDrawable = primary.mapTypeDayIconDrawable ?: secondary.mapTypeDayIconDrawable,
                mapTypeNightIconRes = primary.mapTypeNightIconRes ?: secondary.mapTypeNightIconRes,
                mapTypeNightIconDrawable = primary.mapTypeNightIconDrawable ?: secondary.mapTypeNightIconDrawable,
                overviewModeIconRes = primary.overviewModeIconRes ?: secondary.overviewModeIconRes,
                overviewModeIconDrawable = primary.overviewModeIconDrawable ?: secondary.overviewModeIconDrawable,
                lockModeIconRes = primary.lockModeIconRes ?: secondary.lockModeIconRes,
                lockModeIconDrawable = primary.lockModeIconDrawable ?: secondary.lockModeIconDrawable,
                exitNaviIconRes = primary.exitNaviIconRes ?: secondary.exitNaviIconRes,
                exitNaviIconDrawable = primary.exitNaviIconDrawable ?: secondary.exitNaviIconDrawable,
                naviRightBarBackgroundRes = primary.naviRightBarBackgroundRes ?: secondary.naviRightBarBackgroundRes,
                naviRightBarBackgroundDrawable = primary.naviRightBarBackgroundDrawable ?: secondary.naviRightBarBackgroundDrawable,
                naviBottomBarBackgroundRes = primary.naviBottomBarBackgroundRes ?: secondary.naviBottomBarBackgroundRes,
                naviBottomBarBackgroundDrawable = primary.naviBottomBarBackgroundDrawable ?: secondary.naviBottomBarBackgroundDrawable,
                mapShadowColor = primary.mapShadowColor ?: secondary.mapShadowColor,
                mapType = primary.mapType ?: secondary.mapType
            )
        }
    }
}
