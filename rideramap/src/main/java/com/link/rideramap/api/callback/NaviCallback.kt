package com.link.rideramap.api.callback

import com.amap.api.navi.model.AMapLaneInfo
import com.amap.api.navi.model.AMapNaviCross
import com.amap.api.navi.model.AMapNaviRouteNotifyData
import com.link.rideramap.api.dto.NavigationInfo

/**
 * <AUTHOR>
 * @date 2022/6/29
 */

open class NaviCallback {
    open fun onNaviDataChanged(navigationInfo: NavigationInfo) {}

    open fun onStartNavi() {}

    open fun onStopNavi() {}

    open fun onGpsSignalWeak(isWeak: Boolean) {}

    open fun onInitNaviFailure() {}

    open fun onInitNaviSuccess() {}

    open fun onGetNavigationText(type: Int, navigationText: String) {}

    open fun showLaneInfo(aMapLaneInfo: AMapLaneInfo) {}

    open fun onArriveDestination() {}

    open fun onNaviRouteNotify(aMapNaviRouteNotifyData: AMapNaviRouteNotifyData) {}

    open fun showCross(aMapNaviCross: AMapNaviCross) {}

    open fun onEndEmulatorNavi() {}
}