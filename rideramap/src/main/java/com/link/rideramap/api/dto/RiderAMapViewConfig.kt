package com.link.rideramap.api.dto

import android.graphics.drawable.Drawable
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes

data class RiderAMapViewConfig(
    // 使用资源 ID 或者直接使用 Drawable/ColorInt 对象，提供灵活性
    @DrawableRes val locateButtonIconRes: Int? = null,
    val locateButtonIconDrawable: Drawable? = null,

    @DrawableRes val trafficOnButtonIconRes: Int? = null,
    val trafficOnButtonIconDrawable: Drawable? = null,

    @DrawableRes val trafficOffButtonIconRes: Int? = null,
    val trafficOffButtonIconDrawable: Drawable? = null,

    @DrawableRes val searchButtonIconRes: Int? = null,
    val searchButtonIconDrawable: Drawable? = null,

    @DrawableRes val navigateButtonIconRes: Int? = null,
    val navigateButtonIconDrawable: Drawable? = null,

    @DrawableRes val searchBarBackgroundRes: Int? = null,
    val searchBarBackgroundDrawable: Drawable? = null,

    val searchBarHintText: CharSequence? = null,
    @ColorInt val searchBarHintTextColor: Int? = null,
    @ColorInt val searchBarTextColor: Int? = null,

    @DrawableRes val myLocationMarkerIconRes: Int? = null,
    @DrawableRes val searchResultMarkerIconRes: Int? = null,

    @ColorInt val mapShadowColor: Int? = null,

    val mapType: Int? = null
)