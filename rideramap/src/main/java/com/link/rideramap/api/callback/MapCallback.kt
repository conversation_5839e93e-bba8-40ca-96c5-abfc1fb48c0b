package com.link.rideramap.api.callback

import com.amap.api.navi.model.AMapLaneInfo
import com.amap.api.navi.model.AMapNaviCross
import com.amap.api.navi.model.AMapNaviRouteNotifyData
import com.link.rideramap.api.dto.NavigationInfo

abstract class MapCallback {
    /**
     * 通知 Gps 信号弱
     * @param isSignalWeak 信号是否弱
     */
    open fun onGpsSignalWeak(isSignalWeak: Boolean) {}

    /**
     * 通知导航初始化成功
     */
    open fun onInitNaviSuccess() {}

    /**
     * 通知导航播报信息
     * @param type 播报类型
     * @param content 播报内容
     */
    open fun onGetNavigationText(type: Int, content: String) {}

    /**
     * 显示车道信息
     * @param aMapLaneInfo 车道信息
     * @see com.amap.api.navi.model.AMapLaneInfo
     */
    open fun showLaneInfo(aMapLaneInfo: AMapLaneInfo) {}

    /**
     * 通知到达目的地
     */
    open fun onArriveDestination() {}

    /**
     * 导航路径通知
     * @param aMapNaviRouteNotifyData 导航路径信息
     * @see com.amap.api.navi.model.AMapNaviRouteNotifyData
     */
    open fun onNaviRouteNotify(aMapNaviRouteNotifyData: AMapNaviRouteNotifyData) {}

    /**
     * 显示交叉路口
     * @param aMapNaviCross 交叉路口信息
     * @see com.amap.api.navi.model.AMapNaviCross
     */
    open fun showCross(aMapNaviCross: AMapNaviCross) {}

    /**
     * 通知导航开始
     */
    open fun onStartNavi() {}

    /**
     * 通知导航结束
     */
    open fun onStopNavi() {}

    /**
     * 通知模拟导航结束
     */
    open fun onEndEmulatorNavi() {}

    /**
     * 通知导航数据改变
     * @param navigationInfo 导航信息
     * @see com.link.rideramap.api.dto.NavigationInfo
     */
    open fun onNaviDataChanged(navigationInfo: NavigationInfo) {}

    open fun changeMap(type: Int) {}
    open fun changeTheme(type: Int) {}
}