package com.link.rideramap.api

import android.Manifest
import android.app.Application
import android.content.Context
import android.util.Log
import android.view.Display
import android.view.WindowManager
import com.amap.api.maps.AMap
import com.amap.api.maps.MapsInitializer
import com.amap.api.navi.enums.TravelStrategy
import com.amap.api.navi.model.AMapLaneInfo
import com.amap.api.navi.model.AMapNaviCross
import com.amap.api.navi.model.AMapNaviRouteNotifyData
import com.amap.api.navi.model.NaviPoi
import com.amap.api.services.core.ServiceSettings
import com.link.rideramap.R
import com.link.rideramap.api.callback.MapCallback
import com.link.rideramap.api.callback.PlanRoutesListener
import com.link.rideramap.api.dto.NavigationInfo
import com.link.rideramap.api.dto.PoiAddress
import com.link.rideramap.api.dto.SearchResult
import com.link.rideramap.core.map.MapManagerFacade
import com.link.rideramap.api.callback.NaviCallback
import com.link.rideramap.core.location.domain.entity.LocationInfo
import com.link.rideramap.core.weather.domain.entity.AMapWeatherData
import com.link.rideramap.presentation.display.base.MirrorBasePresentation
import com.link.rideramap.presentation.display.impl.MapMirrorPresentation
import com.link.rideramap.core.infrastructure.pool.MapResourcePoolManager
import java.lang.ref.WeakReference

class SPRiderAMap {
    private val mapManagerFacade = MapManagerFacade()
    private var mPresentation: MirrorBasePresentation? = null
    private val mapCallbacks: MutableList<WeakReference<MapCallback>> = mutableListOf()
    private var application: Application? = null
    private var defaultMode = NAVI_DAYTIME
    private var currentLocation:  LocationInfo? = null
    private var isNaviThemeChanged = false


    internal fun init(application: Application?) {
        MapsInitializer.updatePrivacyShow(application, true, true)
        MapsInitializer.updatePrivacyAgree(application, true)
        ServiceSettings.updatePrivacyShow(application, true, true)
        ServiceSettings.updatePrivacyAgree(application, true)
        ServiceSettings.getInstance().connectionTimeOut = TIMEOUT
        ServiceSettings.getInstance().soTimeOut = TIMEOUT
        <EMAIL> = application
        mapManagerFacade.addNavigationCallback(mNaviDataCallback)
        
        // 初始化地图资源池管理器
        if (application != null) {
            MapResourcePoolManager.initialize(application)
        }
    }
    fun getDefaultMode(): Int {
        return defaultMode
    }

    fun getDefaultModeNavi(): Int {
        return defaultMode
    }
    /**
     * 销毁 [SPRiderAMap]
     */
    fun destroy() {
        mapManagerFacade.removePathRoutesListener()
        mapManagerFacade.removeNavigationCallback(mNaviDataCallback)
        application = null
    }

    /**
     * 获取 [Application]
     */
    fun getApplication(): Application {
        return application!!
    }

    /**
     * 添加回调 [callback]
     * @see MapCallback
     */
    @Synchronized
    fun addCallback(callback: MapCallback) {
        mapCallbacks.add(WeakReference(callback))
    }

    /**
     * 移除回调 [callback]
     * @see MapCallback
     */
    @Synchronized
    fun removeCallback(callback: MapCallback) {
        mapCallbacks.removeIf { it.get() == callback }
    }


    /**
     * 通过 [display] 初始化投屏导航
     */
    @Synchronized
    fun initNaviScreenProjection(display: Display?, isSupportCircularScreen: Boolean = false) {
        // 如果当前已有投屏且显示设备不同，则需要先释放当前投屏
        if (shouldReleaseCurrentPresentation(display)) {
            releaseCurrentPresentation()
        }

        // 只有当没有活跃的投屏且提供了有效的显示设备时才创建新的投屏
        if (mPresentation == null && display != null) {
            createAndShowPresentation(display, isSupportCircularScreen)
        }
    }

    private fun releaseCurrentPresentation() {
        try {
            Log.i(TAG, "释放当前投屏，因为显示设备已更改")
            if (mPresentation?.isShowing == true) {
                mPresentation?.dismiss()
            }
            mPresentation = null
        } catch (th: Throwable) {
            Log.e(TAG, "释放当前投屏失败", th)
        }
    }

    private fun createAndShowPresentation(display: Display, isSupportCircularScreen: Boolean) {
        try {
            val presentation = createMirrorPresentation(
                getApplication(),
                display,
                R.style.PresentationDialog,
                isSupportCircularScreen
            )

            mPresentation = presentation
            presentation.show()
            Log.i(TAG, "成功创建并显示投屏")
        } catch (e: WindowManager.InvalidDisplayException) {
            Log.e(TAG, "无法显示投屏！显示设备可能已被移除", e)
            mPresentation = null
        } catch (e: Exception) {
            Log.e(TAG, "创建或显示投屏时发生未知错误", e)
            mPresentation = null
        }
    }

    private fun shouldReleaseCurrentPresentation(newDisplay: Display?): Boolean {
        return mPresentation != null && mPresentation?.display !== newDisplay
    }


    private fun createMirrorPresentation(
        context: Context, display: Display, theme: Int, isSupportCircularScreen: Boolean
    ): MirrorBasePresentation {
        return MapMirrorPresentation(context, display, theme, isSupportCircularScreen)
    }


    /**
     * 销毁投屏导航
     */
    @Synchronized
    fun releaseNaviScreenProjection() {
        try {
            Log.i(TAG, "releasePresentation $mPresentation")
            mPresentation?.dismiss()
            mPresentation = null
        } catch (throwable: Throwable) {
            Log.e(TAG, "releasePresentation", throwable)
        }
    }

    /**
     * 通过地理编码搜索
     * @param context Context
     * @param latitude 纬度
     * @param longitude 经度
     * @return 返回位置信息
     * @see LocationInfo
     */
    suspend fun geocodeSearch(
        context: Context, latitude: Double, longitude: Double
    ): LocationInfo? {
        return mapManagerFacade.geocodeSearch(context, latitude, longitude)
    }

    /**
     * 通过关键字搜索 POI
     * @param keyword 关键字
     * @return 返回搜索结果
     * @see SearchResult
     */
    suspend fun search(keyword: String): SearchResult {
        return mapManagerFacade.searchByKeyword(getApplication(), keyword)
    }

    /**
     * 通过 POI 搜索
     * @param searchAddress 搜索地址
     * @return 返回搜索结果
     * @see PoiAddress
     * @see SearchResult
     */
    suspend fun searchPOI(searchAddress: PoiAddress): SearchResult {
        return mapManagerFacade.searchPOI(getApplication(), searchAddress)
    }
    /**
     *更改地图模式
     */
    fun changeVirtualNaviMap(type: Int, onTheme: Boolean = false): Int{
        if (isNaviThemeChanged && onTheme){
            isNaviThemeChanged = false
            return defaultMode
        }
        when(type){
            NAVI_DAYTIME -> defaultMode = AMap.MAP_TYPE_NORMAL
            NAVI_NIGHT -> defaultMode = AMap.MAP_TYPE_NIGHT
        }
        mPresentation?.changeMap(defaultMode)
        return defaultMode
    }
    /**
     * 停止导航
     */
    fun stopNavi() {
        mapManagerFacade.stopNavigation()
    }


    /**
     * 选择导航路径
     * @param routeId 路径 id
     */
    fun selectRoute(routeId: Int) {
        mapManagerFacade.selectRoute(routeId)
    }

    /**
     * 传入 [from] 和 [to],计算骑行导航路径,并通过 [planRoutesListener] 回调结果
     * @param from 起点
     * @param to 终点
     * @param planRoutesListener 计算路径结果回调
     */
    fun calculateRideRoute(
        from: NaviPoi,
        to: NaviPoi,
        travelStrategy: TravelStrategy,
        planRoutesListener: PlanRoutesListener
    ) {
        mapManagerFacade.calculateBikeRoute(
            getApplication(), from, to, travelStrategy, planRoutesListener
        )
    }


    /**
     * 传入 [from] 和 [to],计算骑行导航路径,并通过 [planRoutesListener] 回调结果
     * @param from 起点
     * @param to 终点
     * @param planRoutesListener 计算路径结果回调
     */
    fun calculateDriveRoute(
        from: NaviPoi,
        to: NaviPoi,
        pathPlanningStrategy: Int,
        planRoutesListener: PlanRoutesListener
    ) {
        mapManagerFacade.calculateDriveRoute(
            getApplication(), from, to, pathPlanningStrategy, planRoutesListener
        )
    }
    /**
     *之前保留的locationInfo
     */
    fun getLocation(): LocationInfo? {
        return currentLocation
    }
    /**
     *更新保留的locationInfo
     */
    fun setLocation(locationInfo: LocationInfo) {
        currentLocation = locationInfo
    }

    /**
     * 添加导航数据回调
     * @param naviCallback 导航数据回调
     * @see NaviCallback
     */
    internal fun addNaviDataCallback(naviCallback: NaviCallback) {
        mapManagerFacade.addNavigationCallback(naviCallback)
    }

    internal fun removeNaviDataCallback(naviCallback: NaviCallback) {
        mapManagerFacade.removeNavigationCallback(naviCallback)
    }
    /**
     * 获取定位位置
     * 需要权限 [Manifest.permission.ACCESS_FINE_LOCATION]
     * 需要权限 [Manifest.permission.ACCESS_COARSE_LOCATION]
     * @return 返回位置信息
     * @see LocationInfo
     */
    suspend fun getLbsLocation(): LocationInfo {
        return mapManagerFacade.getLbsLocation()
    }

    suspend fun startLbsLocation(): LocationInfo {
        return mapManagerFacade.startLbsLocation()
    }

    internal fun stopLbsLocation() {
        mapManagerFacade.stopLbsLocation()
    }

    fun startNavi() {
        mapManagerFacade.startNavigation()
        mPresentation?.changeMap(defaultMode)
    }

    fun setNaviType(type: Boolean) {
        mapManagerFacade.setNavigationType(type)
    }

    fun isNavi(): Boolean {
        return mapManagerFacade.isNavigating()
    }


    private val mNaviDataCallback = object : NaviCallback() {

        override fun onGpsSignalWeak(isWeak: Boolean) {
            super.onGpsSignalWeak(isWeak)
            mapCallbacks.forEach {
                it.get()?.onGpsSignalWeak(isWeak)
            }
        }

        override fun onInitNaviSuccess() {
            super.onInitNaviSuccess()
            mapCallbacks.forEach {
                it.get()?.onInitNaviSuccess()
            }
        }

        override fun onGetNavigationText(type: Int, navigationText: String) {
            super.onGetNavigationText(type, navigationText)
            mapCallbacks.forEach {
                it.get()?.onGetNavigationText(type, navigationText)
            }
        }

        override fun showLaneInfo(aMapLaneInfo: AMapLaneInfo) {
            super.showLaneInfo(aMapLaneInfo)
            mapCallbacks.forEach {
                it.get()?.showLaneInfo(
                    aMapLaneInfo
                )
            }
        }

        override fun onArriveDestination() {
            super.onArriveDestination()
            mapCallbacks.forEach {
                it.get()?.onArriveDestination()
            }
        }

        override fun onNaviRouteNotify(aMapNaviRouteNotifyData: AMapNaviRouteNotifyData) {
            super.onNaviRouteNotify(aMapNaviRouteNotifyData)
            mapCallbacks.forEach {
                it.get()?.onNaviRouteNotify(aMapNaviRouteNotifyData)
            }
        }

        override fun showCross(aMapNaviCross: AMapNaviCross) {
            super.showCross(aMapNaviCross)
            mapCallbacks.forEach {
                it.get()?.showCross(aMapNaviCross)
            }
        }


        override fun onStartNavi() {
            super.onStartNavi()
            mapCallbacks.forEach {
                it.get()?.onStartNavi()
            }
        }

        override fun onStopNavi() {
            super.onStopNavi()
            mapCallbacks.forEach {
                it.get()?.onStopNavi()
            }
        }

        override fun onEndEmulatorNavi() {
            super.onEndEmulatorNavi()
            mapCallbacks.forEach {
                it.get()?.onEndEmulatorNavi()
            }
        }

        override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
            super.onNaviDataChanged(navigationInfo)
            mapCallbacks.forEach {
                it.get()?.onNaviDataChanged(navigationInfo)
            }
        }
    }

    fun onMapchange(type: Int){//点击按钮切换地图昼夜模式
        val realtype = changeVirtualNaviMap(type)
        isNaviThemeChanged = true
        mapCallbacks.forEach{
            it.get()?.changeMap(realtype)
        }
    }

    suspend fun getWeatherInfo(): AMapWeatherData {
        val weather = mapManagerFacade.getWeatherData()
        return weather.getOrNull() ?: AMapWeatherData()
    }

    companion object {
        /** 获取 SPRiderAMap 的单例 */
        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            SPRiderAMap()
        }
        private const val TIMEOUT = 5000
        private const val TAG = "SPRiderAMap"
        /**协议昼夜白天模式: 0*/
        private const val NAVI_DAYTIME = 0
        /**协议昼夜黑夜模式: 1*/
        private const val NAVI_NIGHT = 1
    }
}