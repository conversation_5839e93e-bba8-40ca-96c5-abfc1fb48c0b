package com.link.rideramap.api.dto

/**
 * 导航信息类
 * @property curLink 小路段索引
 * @property curPoint 小路段上当前点索引
 * @property curStep 大路段索引
 * @property naviType 导航信息类型
 * @property pathId 线路ID
 * @property iconType 导航转向图标类型
 * @property curStepRetainDistance 当前路段剩余距离
 * @property curStepRetainTime 当前路段剩余时间
 * @property pathRetainDistance 路径剩余距离
 * @property pathRetainTime 路径剩余时间
 * @property currentRoadName 当前道路名称
 * @property nextRoadName 下一道路名称
 * @property routeRemainLightCount 路径剩余红绿灯个数
 * @property mapType 地图类型
 * @property turnIconName 导航转向图标名称
 * @property turnKind 导航转向类型
 */
data class NavigationInfo(
    var curLink: Int,
    var curPoint: Int,
    var curStep: Int,
    var naviType: Int,
    var pathId: Long,
    var iconType: Int,
    var curStepRetainDistance: Int,
    var curStepRetainTime: Int,
    var pathRetainDistance: Int,
    var pathRetainTime: Int,
    var currentRoadName: String ,
    var nextRoadName: String,
    var routeRemainLightCount: Int,
    var mapType: Int,
    var turnIconName: String ,
    var turnKind: Int
)