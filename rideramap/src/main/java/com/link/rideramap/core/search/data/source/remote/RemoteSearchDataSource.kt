package com.link.rideramap.core.search.data.source.remote

import android.content.Context
import com.link.rideramap.api.dto.PoiAddress
import com.link.rideramap.api.dto.SearchResult
import com.link.rideramap.core.location.domain.entity.LocationInfo

internal interface RemoteSearchDataSource {

    suspend fun search(
        context: Context,
        keyword: String,
        locationInfo: LocationInfo
    ): SearchResult

    suspend fun searchPOI(
        context: Context,
        searchAddress: PoiAddress,
        locationInfo: LocationInfo
    ): SearchResult

    suspend fun geocodeSearch(context: Context, latitude: Double, longitude: Double): LocationInfo
}