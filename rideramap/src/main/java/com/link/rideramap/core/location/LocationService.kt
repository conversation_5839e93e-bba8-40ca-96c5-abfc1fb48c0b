package com.link.rideramap.core.location

import com.link.rideramap.core.location.domain.entity.LocationInfo

/**
 * 位置服务接口 - 定义位置相关功能的契约
 */
interface LocationService {
    
    /**
     * 获取LBS位置信息
     */
    suspend fun getLbsLocation(): LocationInfo
    
    /**
     * 启动LBS定位
     */
    suspend fun startLbsLocation(): LocationInfo
    
    /**
     * 停止LBS定位
     */
    fun stopLbsLocation()
    
    /**
     * 销毁位置服务
     */
    fun destroy()
} 