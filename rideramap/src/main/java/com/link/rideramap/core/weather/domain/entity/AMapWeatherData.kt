package com.link.rideramap.core.weather.domain.entity

/**
 * <AUTHOR>
 * @date 2022/8/25
 */
data class AMapWeatherData(
    val adCode: String = "",
    var city: String = "",
    var humidity: String = "",
    var province: String = "",
    var reportTime: String = "",
    val temperature: String = "",
    var weather: String = "",
    var windDirection: String = "",
    var windPower: String = ""
)