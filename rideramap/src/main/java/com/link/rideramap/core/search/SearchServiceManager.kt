package com.link.rideramap.core.search

import android.content.Context
import com.link.rideramap.api.dto.PoiAddress
import com.link.rideramap.api.dto.SearchResult
import com.link.rideramap.core.location.domain.entity.LocationInfo
import com.link.rideramap.core.search.data.repository.SearchRepositoryImpl
import com.link.rideramap.core.search.data.source.remote.AMapRemoteSearchDataSourceImpl
import com.link.rideramap.core.search.domain.repository.SearchRepository

/**
 * 搜索服务管理器 - 专门负责搜索相关功能
 * 
 * 职责：
 * - 地理编码搜索
 * - POI搜索
 * - 关键字搜索
 * - 搜索结果处理
 */
class SearchServiceManager : SearchService {
    
    private val searchRepository: SearchRepository = 
        SearchRepositoryImpl(remoteSearchDataSource = AMapRemoteSearchDataSourceImpl())
    
    /**
     * 地理编码搜索 - 根据经纬度获取地址信息
     */
    override suspend fun geocodeSearch(
        context: Context,
        latitude: Double,
        longitude: Double
    ): LocationInfo? {
        return searchRepository.geocodeSearch(context, latitude, longitude)
    }
    
    /**
     * 关键字搜索
     */
    override suspend fun searchByKeyword(
        context: Context,
        keyword: String,
        currentLocation: LocationInfo
    ): SearchResult {
        return searchRepository.search(context, keyword, currentLocation)
    }
    
    /**
     * POI搜索
     */
    override suspend fun searchPOI(
        context: Context,
        searchAddress: PoiAddress,
        currentLocation: LocationInfo
    ): SearchResult {
        return searchRepository.searchPOI(context, searchAddress, currentLocation)
    }
} 