package com.link.rideramap.core.location.data.repository

import com.link.rideramap.core.location.data.source.AMapLocationDataSourceImpl
import com.link.rideramap.core.location.data.source.LocationDataSource
import com.link.rideramap.core.location.domain.entity.LocationInfo
import com.link.rideramap.core.location.domain.repository.LocationRepository

/**
 * Created on 2022/12/12.
 * <AUTHOR>
 */
internal class LocationRepositoryImpl(
    private val locationDataSource: LocationDataSource = AMapLocationDataSourceImpl()
) : LocationRepository {
    override suspend fun getLbsLocation(): LocationInfo {
        return locationDataSource.getLbsLocation()
    }

    override suspend fun startLbsLocation(): LocationInfo {
        return locationDataSource.startLbsLocation()
    }

    override fun destroy() {
        locationDataSource.destroy()
    }
}