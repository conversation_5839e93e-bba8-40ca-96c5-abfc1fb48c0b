package com.link.rideramap.core.map

import android.content.Context
import com.amap.api.navi.enums.TravelStrategy
import com.amap.api.navi.model.AMapNaviPath
import com.amap.api.navi.model.NaviPoi
import com.link.rideramap.api.callback.NaviCallback
import com.link.rideramap.api.callback.PlanRoutesListener
import com.link.rideramap.api.dto.PoiAddress
import com.link.rideramap.api.dto.SearchResult
import com.link.rideramap.core.location.LocationServiceManager
import com.link.rideramap.core.location.domain.entity.LocationInfo
import com.link.rideramap.core.navigation.NavigationManager
import com.link.rideramap.core.search.SearchServiceManager
import com.link.rideramap.core.weather.WeatherServiceManager
import com.link.rideramap.core.weather.domain.entity.AMapWeatherData

/**
 * 地图管理器门面 - 作为地图相关服务的统一入口
 * 
 * 职责：
 * - 协调各个专门的服务管理器
 * - 提供统一的API接口
 * - 管理服务间的依赖关系
 */
internal class MapManagerFacade {
    
    // 各个专门的服务管理器
    private val navigationManager = NavigationManager()
    private val locationServiceManager = LocationServiceManager()
    private val searchServiceManager = SearchServiceManager()
    private val weatherServiceManager = WeatherServiceManager()
    
    // ===================== 导航相关方法 =====================
    
    /**
     * 添加导航回调
     */
    fun addNavigationCallback(callback: NaviCallback) {
        navigationManager.addNavigationCallback(callback)
    }
    
    /**
     * 移除导航回调
     */
    fun removeNavigationCallback(callback: NaviCallback) {
        navigationManager.removeNavigationCallback(callback)
    }
    
    /**
     * 计算骑行路径
     */
    fun calculateBikeRoute(
        context: Context,
        from: NaviPoi,
        to: NaviPoi,
        travelStrategy: TravelStrategy,
        planRoutesListener: PlanRoutesListener,
    ) {
        navigationManager.calculateBikeRoute(context, from, to, travelStrategy, planRoutesListener)
    }
    
    /**
     * 计算驾车路径
     */
    fun calculateDriveRoute(
        context: Context,
        from: NaviPoi,
        to: NaviPoi,
        pathPlanningStrategy: Int,
        planRoutesListener: PlanRoutesListener,
    ) {
        navigationManager.calculateDriveRoute(context, from, to, pathPlanningStrategy, planRoutesListener)
    }
    
    /**
     * 开始导航
     */
    fun startNavigation() {
        navigationManager.startNavigation()
    }
    
    /**
     * 停止导航
     */
    fun stopNavigation() {
        navigationManager.stopNavigation()
    }
    
    /**
     * 选择路线
     */
    fun selectRoute(routeId: Int): Boolean {
        return navigationManager.selectRoute(routeId)
    }
    
    /**
     * 获取导航路径
     */
    fun getNavigationPaths(): HashMap<Int, AMapNaviPath>? {
        return navigationManager.getNavigationPaths()
    }
    
    /**
     * 检查是否正在导航
     */
    fun isNavigating(): Boolean {
        return navigationManager.isNavigating()
    }
    
    /**
     * 设置导航类型
     */
    fun setNavigationType(useEmulator: Boolean) {
        navigationManager.setNavigationType(useEmulator)
    }
    
    /**
     * 移除路径规划监听器
     */
    fun removePathRoutesListener() {
        navigationManager.removePathRoutesListener()
    }
    
    // ===================== 位置服务相关方法 =====================
    
    /**
     * 获取LBS位置信息
     */
    suspend fun getLbsLocation(): LocationInfo {
        return locationServiceManager.getLbsLocation()
    }
    
    /**
     * 启动LBS定位
     */
    suspend fun startLbsLocation(): LocationInfo {
        return locationServiceManager.startLbsLocation()
    }
    
    /**
     * 停止LBS定位
     */
    fun stopLbsLocation() {
        locationServiceManager.stopLbsLocation()
    }
    
    // ===================== 搜索服务相关方法 =====================
    
    /**
     * 地理编码搜索
     */
    suspend fun geocodeSearch(
        context: Context,
        latitude: Double,
        longitude: Double
    ): LocationInfo? {
        return searchServiceManager.geocodeSearch(context, latitude, longitude)
    }
    
    /**
     * 关键字搜索
     */
    suspend fun searchByKeyword(context: Context, keyword: String): SearchResult {
        val currentLocation = locationServiceManager.getLbsLocation()
        return searchServiceManager.searchByKeyword(context, keyword, currentLocation)
    }
    
    /**
     * POI搜索
     */
    suspend fun searchPOI(context: Context, searchAddress: PoiAddress): SearchResult {
        val currentLocation = locationServiceManager.getLbsLocation()
        return searchServiceManager.searchPOI(context, searchAddress, currentLocation)
    }
    
    // ===================== 天气服务相关方法 =====================
    
    /**
     * 获取天气数据
     */
    suspend fun getWeatherData(): Result<AMapWeatherData> {
        return weatherServiceManager.getWeatherData()
    }
    
    // ===================== 生命周期管理 =====================
    
    /**
     * 销毁地图管理器门面
     */
    fun destroy() {
        navigationManager.destroy()
        locationServiceManager.destroy()
        // 搜索和天气服务管理器目前不需要特殊的销毁处理
    }
} 