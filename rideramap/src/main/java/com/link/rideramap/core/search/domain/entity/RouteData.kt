package com.link.rideramap.core.search.domain.entity

import com.amap.api.maps.model.LatLng
import com.amap.api.navi.model.AMapNaviPath

/**
 * <AUTHOR>
 * @date 2022/8/9
 */

data class RouteData(
    val naviPath: AMapNaviPath,
    val allDistance: Int = naviPath.allLength,
    val allTime: Int = naviPath.allTime,
    val tollCost: Int = naviPath.tollCost,
    val trafficLights: Int = naviPath.trafficLightCount,
    val routeId: Int = -10001,
    val formattedDistance: String = "",
    val formattedTime: String = ""
) {
    val points: List<LatLng>
        get() = naviPath.coordList?.map {
            LatLng(it.latitude, it.longitude)
        } ?: arrayListOf()
}
