package com.link.rideramap.core.location

import com.link.rideramap.core.location.data.repository.LocationRepositoryImpl
import com.link.rideramap.core.location.domain.entity.LocationInfo

/**
 * 位置服务管理器 - 专门负责位置相关功能
 * 
 * 职责：
 * - LBS定位服务管理
 * - 定位数据获取
 * - 定位服务生命周期管理
 */
class LocationServiceManager : LocationService {
    
    private val locationRepository = LocationRepositoryImpl()
    
    /**
     * 获取LBS位置信息
     */
    override suspend fun getLbsLocation(): LocationInfo {
        return locationRepository.getLbsLocation()
    }
    
    /**
     * 启动LBS定位
     */
    override suspend fun startLbsLocation(): LocationInfo {
        return locationRepository.startLbsLocation()
    }
    
    /**
     * 停止LBS定位
     */
    override fun stopLbsLocation() {
        locationRepository.destroy()
    }
    
    /**
     * 销毁位置服务管理器
     */
    override fun destroy() {
        locationRepository.destroy()
    }
} 