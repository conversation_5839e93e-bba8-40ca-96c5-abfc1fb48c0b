package com.link.rideramap.core.map.listener

import com.amap.api.navi.MyNaviListener
import com.amap.api.navi.model.AMapLaneInfo
import com.amap.api.navi.model.AMapModelCross
import com.amap.api.navi.model.AMapNaviCameraInfo
import com.amap.api.navi.model.AMapNaviCross
import com.amap.api.navi.model.AMapNaviLocation
import com.amap.api.navi.model.AMapNaviRouteNotifyData
import com.amap.api.navi.model.AMapNaviTrafficFacilityInfo
import com.amap.api.navi.model.AMapServiceAreaInfo
import com.amap.api.navi.model.AimLessModeCongestionInfo
import com.amap.api.navi.model.AimLessModeStat
import com.amap.api.navi.model.InnerNaviInfo
import com.amap.api.navi.model.NaviCongestionInfo
import com.amap.api.navi.model.NaviPath

internal abstract class AMapNaviListenerAdapter : MyNaviListener {
    override fun onInnerNaviInfoUpdate(innerNaviInfo: InnerNaviInfo) {}

    override fun onInnerNaviInfoUpdate(innerNaviInfos: Array<InnerNaviInfo>) {}

    override fun onUpdateTmcStatus(naviCongestionInfo: NaviCongestionInfo?) {}

    override fun onSelectRouteId(i: Int) {}

    override fun updateBackupPath(naviPaths: Array<NaviPath>) {}

    override fun onSuggestChangePath(l: Long, l1: Long, i: Int, s: String) {}

    override fun onUpdateNaviPath() {}

    override fun onUpdateGpsSignalStrength(i: Int) {}

    override fun onTrafficStatusUpdate() {}

    override fun onLocationChange(aMapNaviLocation: AMapNaviLocation) {}

    @Deprecated("")
    override fun onGetNavigationText(s: String) {
    }

    @Deprecated("")
    override fun onCalculateRouteFailure(i: Int) {
    }

    override fun onReCalculateRouteForYaw() {}

    override fun onReCalculateRouteForTrafficJam() {}

    override fun onArrivedWayPoint(i: Int) {}

    override fun onGpsOpenStatus(b: Boolean) {}

    override fun onServiceAreaUpdate(aMapServiceAreaInfos: Array<AMapServiceAreaInfo>?) {}

    override fun showModeCross(aMapModelCross: AMapModelCross?) {}

    override fun hideModeCross() {}

    @Deprecated("")
    override fun showLaneInfo(
        aMapLaneInfos: Array<AMapLaneInfo>,
        bytes: ByteArray,
        bytes1: ByteArray
    ) {
    }

    @Deprecated("")
    override fun onCalculateRouteSuccess(ints: IntArray) {
    }

    @Deprecated("")
    override fun notifyParallelRoad(i: Int) {
    }

    @Deprecated("")
    override fun OnUpdateTrafficFacility(aMapNaviTrafficFacilityInfos: Array<AMapNaviTrafficFacilityInfo>) {
    }

    @Deprecated("")
    override fun OnUpdateTrafficFacility(aMapNaviTrafficFacilityInfo: AMapNaviTrafficFacilityInfo) {
    }

    @Deprecated("")
    override fun updateAimlessModeStatistics(aimLessModeStat: AimLessModeStat) {
    }

    @Deprecated("")
    override fun updateAimlessModeCongestionInfo(aimLessModeCongestionInfo: AimLessModeCongestionInfo) {
    }

    override fun onPlayRing(i: Int) {}

    override fun updateCameraInfo(aMapNaviCameraInfos: Array<AMapNaviCameraInfo>?) {}

    override fun updateIntervalCameraInfo(
        aMapNaviCameraInfo: AMapNaviCameraInfo,
        aMapNaviCameraInfo1: AMapNaviCameraInfo, i: Int
    ) {
    }

    override fun showCross(aMapNaviCross: AMapNaviCross?) {}

    override fun hideCross() {}

    override fun showLaneInfo(aMapLaneInfo: AMapLaneInfo?) {}

    override fun hideLaneInfo() {}

    override fun onNaviRouteNotify(aMapNaviRouteNotifyData: AMapNaviRouteNotifyData?) {}
}