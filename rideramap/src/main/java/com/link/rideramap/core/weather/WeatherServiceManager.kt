package com.link.rideramap.core.weather

import com.link.rideramap.core.weather.data.repository.AMapWeatherRepositoryImpl
import com.link.rideramap.core.weather.domain.entity.AMapWeatherData

/**
 * 天气服务管理器 - 专门负责天气相关功能
 * 
 * 职责：
 * - 天气数据获取
 * - 天气信息处理
 */
class WeatherServiceManager : WeatherService {
    
    private val weatherRepository = AMapWeatherRepositoryImpl()
    
    /**
     * 获取天气数据
     */
    override suspend fun getWeatherData(): Result<AMapWeatherData> {
        return weatherRepository.getWeather()
    }
} 