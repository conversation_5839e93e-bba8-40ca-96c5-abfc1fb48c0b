package com.link.rideramap.core.navigation.utils

import androidx.annotation.DrawableRes
import com.link.rideramap.R

internal object NaviUtil {
    enum class AMapNaviIcon(val type: Int, val desc: String, @DrawableRes val resId: Int) {
        LEFT(2, "左转", R.drawable.amap_left),
        RIGHT(3, "右转", R.drawable.amap_right),
        LEFT_FRONT(4, "左前方", R.drawable.amap_left_front),
        RIGHT_FRONT(5, "右前方", R.drawable.amap_right_front),
        LEFT_BACK(6, "左后方", <PERSON>.drawable.amap_left_back),
        RIGHT_BACK(7, "右后方", R.drawable.amap_right_back),
        LEFT_TURN_AROUND(8, "左转掉头", R.drawable.amap_left_turn_around),
        STRAIGHT(9, "直行", R.drawable.amap_straight),
        ARRIVED_WAYPOINT(10, "到达途径点", <PERSON>.drawable.amap_arrived_waypoint),
        ENTER_ROUNDABOUT(11, "进入环岛", R.drawable.amap_enter_roundabout),
        OUT_ROUNDABOUT(12, "驶出环岛", R.drawable.amap_out_roundabout),
        ARRIVED_SERVICE_AREA(13, "到达服务区", R.drawable.amap_arrived_service_area),
        ARRIVED_TOLLGATE(14, "到达收费站", R.drawable.amap_arrived_tollgate),
        ARRIVED_DESTINATION(15, "到达目的地", R.drawable.amap_arrived_destination),
        ARRIVED_TUNNEL(16, "到达隧道", R.drawable.amap_arrived_tunnel),
        ENTER_LEFT_RING(17, "进入环岛", R.drawable.amap_entry_left_ring),
        LEAVE_LEFT_RING(18, "驶出环岛", R.drawable.amap_leave_left_ring),
        U_TURN_RIGHT(19, "右转掉头", R.drawable.amap_left_turn_around),
        SPECIAL_CONTINUE(20, "顺行", R.drawable.amap_straight),
        ENTRY_RING_LEFT(21, "绕环岛左转", R.drawable.amap_straight),
        ENTRY_RING_RIGHT(22, "绕环岛右转", R.drawable.amap_straight),
        ENTRY_RING_CONTINUE(23, "绕环岛直行", R.drawable.amap_straight),
        ENTRY_RING_U_TURN(24, "绕环岛调头", R.drawable.amap_straight),
        ENTRY_LEFT_RING_LEFT(25, "绕环岛左转", R.drawable.amap_straight),
        ENTRY_LEFT_RING_RIGHT(26, "绕环岛右转", R.drawable.amap_straight),
        ENTRY_LEFT_RING_CONTINUE(27, "绕环岛直行", R.drawable.amap_straight),
        ENTRY_LEFT_RING_U_TURN(28, "绕环岛调头", R.drawable.amap_straight),
        CROSSWALK(29, "通过人行道", R.drawable.amap_crosswalk_or_walk_road),
        OVERPASS(30, "通过过街天桥", R.drawable.amap_bridge_or_overpass),
        UNDERPASS(31, "通过地下通道", R.drawable.amap_underpass),
        SQUARE(32, "通过广场", R.drawable.amap_square),
        PARK(33, "通过公园", R.drawable.amap_park),
        STAIRCASE(34, "通过扶梯", R.drawable.amap_by_escalator_or_staircase),
        LIFT(35, "通过直梯", R.drawable.amap_by_elevator_or_lift),
        CABLE_WAY(36, "通过索道", R.drawable.amap_cableway),
        SKY_CHANNEL(37, "通过空中通道", R.drawable.amap_sky_channel),
        CHANNEL(38, "穿越通道", R.drawable.amap_channel),
        WALK_ROAD(39, "通过人行道", R.drawable.amap_crosswalk_or_walk_road),
        CRUISE_ROUTE(40, "通过游船路线", R.drawable.amap_cruise_route),
        SIGHTSEEING_BUS_LINE(41, "通过观光车路线", R.drawable.amap_sightseeing_busline),
        SLIDE_WAY(42, "通过滑道", R.drawable.amap_slideway),
        LADDER(43, "通过阶梯", R.drawable.amap_ladder_or_by_stair),
        SLOPE(44, "通过斜坡", R.drawable.amap_slope),
        BRIDGE(45, "通过桥", R.drawable.amap_bridge_or_overpass),
        FERRY(46, "通过轮渡", R.drawable.amap_ferry),
        SUBWAY(47, "通过地铁通道", R.drawable.amap_subway),
        ENTER_BUILDING(48, "进入建筑物", R.drawable.amap_enter_building),
        LEAVE_BUILDING(49, "离开建筑物", R.drawable.amap_leave_building),
        BY_ELEVATOR(50, "电梯换层", R.drawable.amap_by_elevator_or_lift),
        BY_STAIR(51, "楼梯换层", R.drawable.amap_ladder_or_by_stair),
        BY_ESCALATOR(52, "扶梯换层", R.drawable.amap_by_escalator_or_staircase);


        companion object {

            private var previousResId = LEFT.resId

            private var previousNaviType = LEFT.type

            private var previousName = LEFT.desc

            fun getByType(type: Int): AMapNaviIcon? {
                return enumValues<AMapNaviIcon>().find { type == it.type }
            }

            fun getResId(type: Int): Int {
                if (type == previousNaviType) {
                    return previousResId
                }
                previousResId = getByType(type)?.resId ?: LEFT.resId
                previousNaviType = type
                return previousResId
            }

            fun getName(type: Int): String {
                if (type == previousNaviType) {
                    return previousName
                }
                previousResId = getByType(type)?.resId ?: LEFT.resId
                previousNaviType = type
                return previousName
            }
        }
    }
}