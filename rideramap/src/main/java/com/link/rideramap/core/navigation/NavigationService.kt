package com.link.rideramap.core.navigation

import android.content.Context
import com.amap.api.navi.enums.TravelStrategy
import com.amap.api.navi.model.AMapNaviPath
import com.amap.api.navi.model.NaviPoi
import com.link.rideramap.api.callback.NaviCallback
import com.link.rideramap.api.callback.PlanRoutesListener

/**
 * 导航服务接口 - 定义导航相关功能的契约
 */
interface NavigationService {
    
    /**
     * 添加导航回调
     */
    fun addNavigationCallback(callback: NaviCallback)
    
    /**
     * 移除导航回调
     */
    fun removeNavigationCallback(callback: NaviCallback)
    
    /**
     * 计算骑行路径
     */
    fun calculateBikeRoute(
        context: Context,
        startPoint: NaviPoi,
        endPoint: NaviPoi,
        travelStrategy: TravelStrategy,
        planRoutesListener: PlanRoutesListener,
    )
    
    /**
     * 计算驾车路径
     */
    fun calculateDriveRoute(
        context: Context,
        startPoint: NaviPoi,
        endPoint: NaviPoi,
        pathPlanningStrategy: Int,
        planRoutesListener: PlanRoutesListener,
    )
    
    /**
     * 开始导航
     */
    fun startNavigation()
    
    /**
     * 停止导航
     */
    fun stopNavigation()
    
    /**
     * 选择路线
     */
    fun selectRoute(routeId: Int): Boolean
    
    /**
     * 获取导航路径
     */
    fun getNavigationPaths(): HashMap<Int, AMapNaviPath>?
    
    /**
     * 检查是否正在导航
     */
    fun isNavigating(): Boolean
    
    /**
     * 设置导航类型
     */
    fun setNavigationType(isEmulatorNavigation: Boolean)
    
    /**
     * 移除路径规划监听器
     */
    fun removePathRoutesListener()
    
    /**
     * 销毁导航服务
     */
    fun destroy()
} 