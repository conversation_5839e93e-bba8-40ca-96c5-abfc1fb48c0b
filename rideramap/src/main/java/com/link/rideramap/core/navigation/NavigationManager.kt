package com.link.rideramap.core.navigation

import android.content.Context
import com.amap.api.maps.AMapException
import com.amap.api.navi.AMapNavi
import com.amap.api.navi.AMapNaviListener
import com.amap.api.navi.enums.NaviType
import com.amap.api.navi.enums.TravelStrategy
import com.amap.api.navi.model.AMapCalcRouteResult
import com.amap.api.navi.model.AMapNaviPath
import com.amap.api.navi.model.NaviInfo
import com.amap.api.navi.model.NaviPoi
import com.link.rideramap.api.callback.NaviCallback
import com.link.rideramap.api.callback.PlanRoutesListener
import com.link.rideramap.api.dto.MapCalcRouteResult
import com.link.rideramap.api.dto.NavigationInfo
import com.link.rideramap.core.map.listener.AMapNaviListenerAdapter
import com.link.rideramap.core.search.domain.entity.RouteData
import com.link.rideramap.core.navigation.utils.NaviUtil
import java.lang.ref.WeakReference

/**
 * 导航管理器 - 专门负责导航相关功能
 * 
 * 职责：
 * - 路径计算（骑行、驾车）
 * - 导航启动/停止控制
 * - 导航状态管理
 * - 导航回调管理
 * - 导航信息更新
 */
class NavigationManager : NavigationService {
    
    private var isNavigating = false
    private var currentNaviType = NaviType.GPS
    private var aMapNavi: AMapNavi? = null
    private var pathRoutesListener: PlanRoutesListener? = null
    
    // 导航回调管理
    private val navigationCallbacks: MutableList<WeakReference<NaviCallback>> = mutableListOf()
    
    // 当前导航信息
    private val currentNavigationInfo by lazy {
        NavigationInfo(
            curLink = 0,
            curPoint = 0,
            curStep = 0,
            naviType = 0,
            pathId = 0,
            iconType = 0,
            curStepRetainDistance = 0,
            curStepRetainTime = 0,
            pathRetainDistance = 0,
            pathRetainTime = 0,
            currentRoadName = "",
            nextRoadName = "",
            routeRemainLightCount = 0,
            mapType = 1,
            turnIconName = "",
            turnKind = 0
        )
    }
    
    // 导航监听器
    private val navigationListener: AMapNaviListener = object : AMapNaviListenerAdapter() {
        override fun onGetNavigationText(type: Int, navigationText: String) {
            notifyCallbacks { it.onGetNavigationText(type, navigationText) }
        }

        override fun onCalculateRouteFailure(calcRouteResult: AMapCalcRouteResult) {
            val mapCalcRouteResult = MapCalcRouteResult(
                calcRouteResult.errorCode,
                calcRouteResult.errorDetail,
                calcRouteResult.errorDescription,
                arrayListOf()
            )
            pathRoutesListener?.onCalculateRouteFailure(mapCalcRouteResult)
        }

        override fun onCalculateRouteSuccess(calcRouteResult: AMapCalcRouteResult) {
            val navigationPaths = getNavigationPaths()
            val routes = navigationPaths?.map {
                RouteData(naviPath = it.value, routeId = it.key)
            } ?: listOf()
            
            val mapCalcRouteResult = MapCalcRouteResult(
                errorCode = calcRouteResult.errorCode,
                errorDetail = calcRouteResult.errorDetail,
                errorDescription = calcRouteResult.errorDescription,
                routeList = routes
            )
            pathRoutesListener?.onCalculateRouteSuccess(mapCalcRouteResult)
        }

        override fun onInitNaviFailure() {
            notifyCallbacks { it.onInitNaviFailure() }
        }

        override fun onInitNaviSuccess() {
            notifyCallbacks { it.onInitNaviSuccess() }
        }

        override fun onStartNavi(type: Int) {
            isNavigating = true
            notifyCallbacks { it.onStartNavi() }
        }

        override fun onEndEmulatorNavi() {
            isNavigating = false
            aMapNavi?.stopGPS()
            notifyCallbacks { it.onEndEmulatorNavi() }
        }

        override fun onArriveDestination() {
            isNavigating = false
            aMapNavi?.stopGPS()
            notifyCallbacks { it.onArriveDestination() }
        }

        override fun onNaviInfoUpdate(amapNaviInfo: NaviInfo) {
            updateNavigationInfo(amapNaviInfo)
            notifyCallbacks { it.onNaviDataChanged(currentNavigationInfo) }
        }

        override fun onGpsSignalWeak(weakSignal: Boolean) {
            notifyCallbacks { it.onGpsSignalWeak(weakSignal) }
        }

        override fun onStopNavi() {
            isNavigating = false
            aMapNavi?.stopGPS()
            notifyCallbacks { it.onStopNavi() }
        }
    }
    
    /**
     * 添加导航回调
     */
    @Synchronized
    override fun addNavigationCallback(callback: NaviCallback) {
        navigationCallbacks.add(WeakReference(callback))
    }
    
    /**
     * 移除导航回调
     */
    @Synchronized
    override fun removeNavigationCallback(callback: NaviCallback) {
        navigationCallbacks.removeIf { it.get() == callback }
    }
    
    /**
     * 计算骑行路径
     */
    override fun calculateBikeRoute(
        context: Context,
        startPoint: NaviPoi,
        endPoint: NaviPoi,
        travelStrategy: TravelStrategy,
        planRoutesListener: PlanRoutesListener,
    ) {
        pathRoutesListener = planRoutesListener
        try {
            initializeNaviIfNeeded(context)
            aMapNavi?.calculateRideRoute(startPoint, endPoint, travelStrategy)
        } catch (e: AMapException) {
            e.printStackTrace()
        }
    }
    
    /**
     * 计算驾车路径
     */
    override fun calculateDriveRoute(
        context: Context,
        startPoint: NaviPoi,
        endPoint: NaviPoi,
        pathPlanningStrategy: Int,
        planRoutesListener: PlanRoutesListener,
    ) {
        pathRoutesListener = planRoutesListener
        try {
            initializeNaviIfNeeded(context)
            aMapNavi?.calculateDriveRoute(startPoint, endPoint, null, pathPlanningStrategy)
        } catch (e: AMapException) {
            e.printStackTrace()
        }
    }
    
    /**
     * 开始导航
     */
    override fun startNavigation() {
        if (!AMapNavi.isNaviStarted()) {
            aMapNavi?.run {
                startGPS()
                startNavi(currentNaviType)
            }
        }
    }
    
    /**
     * 停止导航
     */
    override fun stopNavigation() {
        aMapNavi?.stopNavi()
    }
    
    /**
     * 选择路线
     */
    override fun selectRoute(routeId: Int): Boolean {
        return aMapNavi?.selectRouteId(routeId) == true
    }
    
    /**
     * 获取导航路径
     */
    override fun getNavigationPaths(): HashMap<Int, AMapNaviPath>? {
        return aMapNavi?.naviPaths
    }
    
    /**
     * 检查是否正在导航
     */
    override fun isNavigating(): Boolean = isNavigating
    
    /**
     * 设置导航类型
     */
    override fun setNavigationType(isEmulatorNavigation: Boolean) {
        currentNaviType = if (isEmulatorNavigation) NaviType.EMULATOR else NaviType.GPS
    }
    
    /**
     * 移除路径规划监听器
     */
    override fun removePathRoutesListener() {
        pathRoutesListener = null
    }
    
    /**
     * 销毁导航管理器
     */
    override fun destroy() {
        aMapNavi?.removeAMapNaviListener(navigationListener)
        AMapNavi.destroy()
        navigationCallbacks.clear()
        pathRoutesListener = null
    }
    
    // 私有方法
    private fun initializeNaviIfNeeded(context: Context) {
        if (aMapNavi == null) {
            aMapNavi = AMapNavi.getInstance(context).apply {
                addAMapNaviListener(navigationListener)
                setMultipleRouteNaviMode(true)
                setSoTimeout(5000)
                setConnectionTimeout(5000)
                setUseInnerVoice(true, true)
                setEmulatorNaviSpeed(30)
                stopGPS()
            }
        }
    }
    
    private fun updateNavigationInfo(amapNaviInfo: NaviInfo) {
        currentNavigationInfo.apply {
            mapType = 1
            curLink = amapNaviInfo.curLink
            curPoint = amapNaviInfo.curPoint
            curStep = amapNaviInfo.curStep
            naviType = amapNaviInfo.naviType
            pathId = amapNaviInfo.pathId
            iconType = amapNaviInfo.iconType
            curStepRetainDistance = amapNaviInfo.curStepRetainDistance
            curStepRetainTime = amapNaviInfo.curStepRetainTime
            pathRetainTime = amapNaviInfo.pathRetainTime
            pathRetainDistance = amapNaviInfo.pathRetainDistance
            currentRoadName = amapNaviInfo.currentRoadName
            nextRoadName = amapNaviInfo.nextRoadName
            routeRemainLightCount = amapNaviInfo.routeRemainLightCount
            turnIconName = NaviUtil.AMapNaviIcon.getName(iconType)
        }
    }
    
    private fun notifyCallbacks(action: (NaviCallback) -> Unit) {
        navigationCallbacks.forEach { callbackRef ->
            callbackRef.get()?.let(action)
        }
    }
} 