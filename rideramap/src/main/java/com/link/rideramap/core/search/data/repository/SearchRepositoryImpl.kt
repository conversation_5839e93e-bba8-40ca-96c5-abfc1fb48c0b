package com.link.rideramap.core.search.data.repository

import android.content.Context
import com.link.rideramap.api.dto.PoiAddress
import com.link.rideramap.api.dto.SearchResult
import com.link.rideramap.core.location.domain.entity.LocationInfo
import com.link.rideramap.core.search.data.source.remote.RemoteSearchDataSource
import com.link.rideramap.core.search.domain.repository.SearchRepository

internal class SearchRepositoryImpl(
    private val remoteSearchDataSource: RemoteSearchDataSource
) : SearchRepository {

    override suspend fun search(
        context: Context,
        keyword: String,
        locationInfo: LocationInfo
    ): SearchResult =
        remoteSearchDataSource.search(context, keyword, locationInfo)

    override suspend fun searchPOI(
        context: Context,
        searchAddress: PoiAddress,
        locationInfo: LocationInfo
    ): SearchResult =
        remoteSearchDataSource.searchPOI(context, searchAddress, locationInfo)

    override suspend fun geocodeSearch(
        context: Context,
        latitude: Double,
        longitude: Double
    ): LocationInfo {
        return remoteSearchDataSource.geocodeSearch(context, latitude, longitude)
    }
}