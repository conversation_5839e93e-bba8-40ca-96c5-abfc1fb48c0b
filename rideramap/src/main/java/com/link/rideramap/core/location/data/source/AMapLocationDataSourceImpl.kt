package com.link.rideramap.core.location.data.source

import com.amap.api.location.AMapLocationClient
import com.amap.api.location.AMapLocationClientOption
import com.link.rideramap.api.SPRiderAMap
import com.link.rideramap.core.location.domain.entity.LocationInfo
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

/**
 * Created on 2022/12/12.
 * <AUTHOR>
 */
internal class AMapLocationDataSourceImpl : LocationDataSource {

    private val aMapLocationClientOption = AMapLocationClientOption().apply {
        locationMode = AMapLocationClientOption.AMapLocationMode.Hight_Accuracy
        isOnceLocation = true
        isNeedAddress = true
        isMockEnable = false
        isLocationCacheEnable = false
        interval = 1000
        isSensorEnable = true
    }
    private val aMapLocationClient by lazy {
        AMapLocationClient(SPRiderAMap.instance.getApplication()).apply {
            setLocationOption(aMapLocationClientOption)
        }
    }

    override suspend fun getLbsLocation(): LocationInfo {
        try {

            val location = aMapLocationClient.lastKnownLocation
            if (location != null && location.address != null && location.address.isNotEmpty()) {
                return LocationInfo(
                    code = location.errorCode,
                    address = location.address,
                    city = location.city,
                    cityCode = location.cityCode,
                    district = location.district,
                    province = location.province,
                    bearing = location.bearing,
                    latitude = location.latitude,
                    longitude = location.longitude,
                    altitude = location.altitude,
                    adCode = location.adCode
                )
            } else {
                return startLbsLocation()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return LocationInfo()
    }

    override suspend fun startLbsLocation(): LocationInfo =
        suspendCancellableCoroutine { locationListenerContinuation ->
            try {
                aMapLocationClient.setLocationListener {
                    locationListenerContinuation.resume(
                        LocationInfo(
                            code = it.errorCode,
                            address = it.address,
                            city = it.city,
                            cityCode = it.cityCode,
                            district = it.district,
                            province = it.province,
                            bearing = it.bearing,
                            latitude = it.latitude,
                            longitude = it.longitude,
                            altitude = it.altitude,
                            adCode = it.adCode
                        )
                    )
                }
                aMapLocationClient.startLocation()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

    override fun destroy() {
        aMapLocationClient.stopLocation()
        aMapLocationClient.onDestroy()
    }

    companion object {
        private const val TAG = "AMapLocationDataSourceI"
    }
}