package com.link.rideramap.core.search.data.source.remote

import android.content.Context
import android.text.TextUtils
import com.amap.api.services.core.LatLonPoint
import com.amap.api.services.geocoder.GeocodeResult
import com.amap.api.services.geocoder.GeocodeSearch
import com.amap.api.services.geocoder.RegeocodeQuery
import com.amap.api.services.geocoder.RegeocodeResult
import com.amap.api.services.help.Inputtips
import com.amap.api.services.help.InputtipsQuery
import com.amap.api.services.help.Tip
import com.link.rideramap.api.dto.PoiAddress
import com.link.rideramap.api.dto.SearchResult
import com.link.rideramap.core.location.domain.entity.LocationInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

internal class AMapRemoteSearchDataSourceImpl : RemoteSearchDataSource {
    override suspend fun search(
        context: Context,
        keyword: String,
        locationInfo: LocationInfo
    ): SearchResult {
        return withContext(Dispatchers.IO) {
            // 先尝试在当前城市搜索
            val cityResult = searchInScope(context, keyword, locationInfo, true)

            // 如果当前城市没有结果，则扩展到全国范围
            if (cityResult.searchList.isNullOrEmpty()) {
                searchInScope(context, keyword, locationInfo, false)
            } else {
                cityResult
            }
        }
    }

    private suspend fun searchInScope(
        context: Context,
        keyword: String,
        locationInfo: LocationInfo,
        cityLimit: Boolean
    ): SearchResult = suspendCoroutine { continuation ->
        val location = LatLonPoint(locationInfo.latitude, locationInfo.longitude)
        val inputTipsQuery = InputtipsQuery(keyword, locationInfo.cityCode).apply {
            this.location = location
            this.cityLimit = cityLimit // 控制是否限制在当前城市
        }
        val inputTips = Inputtips(context, inputTipsQuery)

        inputTips.setInputtipsListener { list: List<Tip>?, code: Int ->
            val searchResult = SearchResult(code = code, name = keyword, null)
            if (code != 1000) {
                continuation.resume(searchResult)
            } else if (list == null || list.isEmpty()) {
                continuation.resume(searchResult)
            } else {
                val result = list.mapNotNull { tip ->
                    if (tip.point != null) {
                        PoiAddress(
                            adCode = tip.adcode,
                            name = tip.name,
                            poiId = tip.poiID,
                            district = tip.address,
                            latitude = tip.point.latitude,
                            longitude = tip.point.longitude
                        )
                    } else null
                }
                continuation.resume(searchResult.copy(searchList = result))
            }
        }
        inputTips.requestInputtipsAsyn()
    }

    override suspend fun searchPOI(
        context: Context,
        searchAddress: PoiAddress,
        locationInfo: LocationInfo
    ): SearchResult = withContext(Dispatchers.IO) {
        var adCode = searchAddress.adCode
        if (TextUtils.isEmpty(adCode)) {
            adCode = locationInfo.cityCode
        }

        // 先尝试在当前城市搜索
        val cityResult = searchPOIInScope(context, searchAddress.name, adCode, locationInfo, true)

        // 如果当前城市没有结果，则扩展到全国范围
        if (cityResult.searchList.isNullOrEmpty()) {
            searchPOIInScope(context, searchAddress.name, adCode, locationInfo, false)
        } else {
            cityResult
        }
    }

    private suspend fun searchPOIInScope(
        context: Context,
        keyword: String,
        adCode: String,
        locationInfo: LocationInfo,
        cityLimit: Boolean
    ): SearchResult = suspendCoroutine { con ->
        val location = LatLonPoint(locationInfo.latitude, locationInfo.longitude)
        val inputTipsQuery = InputtipsQuery(keyword, adCode).apply {
            this.location = location
            this.cityLimit = cityLimit // 控制是否限制在当前城市
        }
        val inputTips = Inputtips(context, inputTipsQuery)

        inputTips.setInputtipsListener { list: List<Tip>?, i: Int ->
            val searchResult = SearchResult(code = i, name = keyword, null)
            if (i != 1000) {
                con.resume(searchResult)
            } else if (list == null || list.isEmpty()) {
                con.resume(searchResult)
            } else {
                val addresses = list.filter {
                    !TextUtils.isEmpty(it.poiID) && it.point != null
                }.map {
                    PoiAddress(
                        adCode = it.adcode,
                        name = it.name,
                        poiId = it.poiID,
                        district = it.address,
                        latitude = it.point.latitude,
                        longitude = it.point.longitude
                    )
                }
                con.resume(searchResult.copy(searchList = addresses))
            }
        }
        inputTips.requestInputtipsAsyn()
    }

    override suspend fun geocodeSearch(
        context: Context,
        latitude: Double,
        longitude: Double
    ): LocationInfo =
        withContext(Dispatchers.IO) {
            var locationInfo = LocationInfo(code = 1)
            val geocodeSearch = GeocodeSearch(context)
            val regeocodeQuery =
                RegeocodeQuery(
                    LatLonPoint(latitude, longitude),
                    1f,
                    GeocodeSearch.AMAP
                )
            suspendCoroutine { continuation ->
                geocodeSearch.setOnGeocodeSearchListener(object :
                    GeocodeSearch.OnGeocodeSearchListener {
                    override fun onRegeocodeSearched(
                        regeocodeResult: RegeocodeResult?,
                        code: Int
                    ) {
                        if (code != 1000) {
                            locationInfo = locationInfo.copy(code = 404)
                        }
                        regeocodeResult?.let {
                            val regeocodeAddress = regeocodeResult.regeocodeAddress
                            regeocodeAddress?.let {
                                val city = it.city
                                val district = it.district
                                locationInfo = locationInfo.copy(
                                    address = it.formatAddress.ifEmpty { it.province + city + district },
                                    city = city,
                                    district = district,
                                    cityCode = it.cityCode,
                                    code = code,
                                    latitude = latitude,
                                    longitude = longitude
                                )
                            } ?: run {
                                locationInfo = locationInfo.copy(code = 404)
                            }
                        }
                        continuation.resume(locationInfo)
                    }

                    override fun onGeocodeSearched(p0: GeocodeResult?, p1: Int) {
                        continuation.resume(locationInfo)
                    }
                })
                geocodeSearch.getFromLocationAsyn(regeocodeQuery)
            }
        }
}