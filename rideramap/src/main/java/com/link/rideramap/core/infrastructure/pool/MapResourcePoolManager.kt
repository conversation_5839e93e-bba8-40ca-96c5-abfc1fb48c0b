package com.link.rideramap.core.infrastructure.pool

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.link.rideramap.R
import com.link.rideramap.presentation.component.map.VirtualMap
import com.link.rideramap.presentation.component.navigation.VirtualNaviMap
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicInteger

/**
 * 地图资源池管理器
 * 解决高德地图组件数量限制和销毁耗时问题
 */
internal object MapResourcePoolManager {

    private val TAG = MapResourcePoolManager::class.java.simpleName

    // 池配置
    private const val MAX_MAP_ROOT_VIEW_POOL_SIZE = 1

    // 单例根视图（始终复用同一个）
    @SuppressLint("StaticFieldLeak")
    private var singletonMapRootView: PooledMapRootView? = null

    // 清理配置
    private const val IDLE_CLEANUP_DELAY = 120000L

    // 资源池 - 现在只管理完整的根视图
    private val mapRootViewPool = ConcurrentLinkedQueue<PooledMapRootView>()

    // 使用中的资源 - 基于ID管理
    private val activeMapRootViews = mutableSetOf<Int>()

    // 统计信息
    private val createdMapRootViewCount = AtomicInteger(0)

    // 清理任务
    private val mainHandler = Handler(Looper.getMainLooper())
    private var cleanupRunnable: Runnable? = null

    /**
     * 初始化资源管理器
     */
    fun initialize(context: Context) {
        Log.d(TAG, "MapResourcePoolManager initialized")
    }

    /**
     * 获取可用的地图根视图（包含地图组件）
     */
    @Synchronized
    fun acquireMapRootView(context: Context, bundle: Bundle?): PooledMapRootView {
        val startTime = System.currentTimeMillis()

        // 调试：打印当前状态
        Log.d(
            TAG,
            "Before acquire - Active: ${activeMapRootViews.size}, Pooled: ${mapRootViewPool.size}"
        )

        // 额外保护：确保不会超过地图组件限制
        // 由于池大小为1，理论上最多只有1个活跃+1个池中=2个根视图
        // 但为了安全，如果已有活跃根视图且池为空，强制等待或清理
        if (activeMapRootViews.size >= MAX_MAP_ROOT_VIEW_POOL_SIZE && mapRootViewPool.isEmpty()) {
            Log.w(
                TAG,
                "Active map root views limit reached and pool is empty. This should not happen with pool size 1."
            )
        }

        // 采用单例模式：始终复用同一个根视图
        var pooledMapRootView = singletonMapRootView

        if (pooledMapRootView == null) {
            // 第一次创建单例根视图
            Log.d(TAG, "Creating singleton map root view for first time")
            val rootView = createNewMapRootView(context)

            // 获取地图组件
            val virtualMap = rootView.findViewById<VirtualMap>(R.id.virtual_map)
            val virtualNavigationMap = rootView.findViewById<VirtualNaviMap>(R.id.navi_map)

            pooledMapRootView = PooledMapRootView(
                id = createdMapRootViewCount.incrementAndGet(),
                rootView = rootView,
                virtualMap = virtualMap,
                virtualNavigationMap = virtualNavigationMap,
                createdTime = System.currentTimeMillis(),
                isMapInitialized = false,  // 初始状态为未初始化
                isNavigationMapInitialized = false
            )

            // 第一次创建：create(bundle) → onResume()
            initializeMapComponentsFirstTime(pooledMapRootView, bundle)

            // 保存为单例
            singletonMapRootView = pooledMapRootView
        } else {
            Log.d(TAG, "Reusing singleton map root view, id: ${pooledMapRootView.id}")

            // 重要：复用前确保rootView从之前的parent中移除
            pooledMapRootView.rootView.parent?.let { parent ->
                if (parent is ViewGroup) {
                    Log.d(
                        TAG,
                        "Removing rootView from previous parent: ${parent.javaClass.simpleName}"
                    )
                    parent.removeView(pooledMapRootView.rootView)
                }
            }

            // 复用：只需要 onResume()
            resumeMapComponents(pooledMapRootView)
        }

        // 重置视图状态
        resetMapRootView(pooledMapRootView.rootView)

        // 标记为使用中（避免重复添加）
        if (!activeMapRootViews.contains(pooledMapRootView.id)) {
            activeMapRootViews.add(pooledMapRootView.id)
            Log.d(TAG, "Added map root view to active list, id: ${pooledMapRootView.id}")
        } else {
            Log.w(TAG, "Map root view already in active list, id: ${pooledMapRootView.id}")
        }
        pooledMapRootView.acquireTime = System.currentTimeMillis()

        val endTime = System.currentTimeMillis()
        Log.d(
            TAG,
            "acquireMapRootView completed in ${endTime - startTime}ms, id: ${pooledMapRootView.id}"
        )
        Log.d(
            TAG,
            "After acquire - Active: ${activeMapRootViews.size}, Pooled: ${mapRootViewPool.size}"
        )

        return pooledMapRootView
    }

    /**
     * 第一次初始化地图组件 - create(bundle) → onResume()
     */
    private fun initializeMapComponentsFirstTime(
        pooledMapRootView: PooledMapRootView,
        bundle: Bundle?
    ) {
        val startTime = System.currentTimeMillis()

        try {
            Log.d(TAG, "First time initializing map components for id: ${pooledMapRootView.id}")

            // 第一次创建虚拟地图
            pooledMapRootView.virtualMap?.let { virtualMap ->
                Log.d(TAG, "Creating VirtualMap with bundle for first time")
                virtualMap.create(bundle)
                virtualMap.onResume()
                pooledMapRootView.isMapInitialized = true
            }

            // 第一次创建虚拟导航地图
            pooledMapRootView.virtualNavigationMap?.let { virtualNavigationMap ->
                Log.d(TAG, "Creating VirtualNavigationMap with bundle for first time")
                virtualNavigationMap.create(bundle)
                virtualNavigationMap.onResume()
                pooledMapRootView.isNavigationMapInitialized = true
            }

            val endTime = System.currentTimeMillis()
            Log.d(
                TAG,
                "Map components first time initialized in ${endTime - startTime}ms for id: ${pooledMapRootView.id}"
            )

        } catch (e: Exception) {
            Log.e(
                TAG,
                "Failed to initialize map components first time for id: ${pooledMapRootView.id}",
                e
            )
            throw e
        }
    }

    /**
     * 恢复地图组件 - 只调用 onResume()
     */
    private fun resumeMapComponents(pooledMapRootView: PooledMapRootView) {
        val startTime = System.currentTimeMillis()

        try {
            Log.d(TAG, "Resuming map components for id: ${pooledMapRootView.id}")

            // 恢复虚拟地图（不重新create）
            if (pooledMapRootView.isMapInitialized) {
                pooledMapRootView.virtualMap?.let { virtualMap ->
                    Log.d(TAG, "Resuming VirtualMap")
                    virtualMap.onResume()
                }
            }

            // 恢复虚拟导航地图（不重新create）
            if (pooledMapRootView.isNavigationMapInitialized) {
                pooledMapRootView.virtualNavigationMap?.let { virtualNavigationMap ->
                    Log.d(TAG, "Resuming VirtualNavigationMap")
                    virtualNavigationMap.onResume()
                }
            }

            val endTime = System.currentTimeMillis()
            Log.d(
                TAG,
                "Map components resumed in ${endTime - startTime}ms for id: ${pooledMapRootView.id}"
            )

        } catch (e: Exception) {
            Log.e(TAG, "Failed to resume map components for id: ${pooledMapRootView.id}", e)
            throw e
        }
    }

    /**
     * 归还地图根视图到池中
     */
    @Synchronized
    fun releaseMapRootView(pooledMapRootView: PooledMapRootView) {
        val startTime = System.currentTimeMillis()

        if (!activeMapRootViews.remove(pooledMapRootView.id)) {
            Log.w(
                TAG,
                "Trying to release map root view that is not active, id: ${pooledMapRootView.id}"
            )
            return
        }

        // 单例模式：不需要归还到池，只需要暂停状态
        // 检查是否是我们的单例根视图
        if (pooledMapRootView == singletonMapRootView) {
            Log.d(
                TAG,
                "Returning singleton map root view to standby state, id: ${pooledMapRootView.id}"
            )

            // 暂停地图组件但保持生命周期
            pauseMapComponents(pooledMapRootView)

            // 清理视图状态
            cleanupMapRootView(pooledMapRootView.rootView)

            // 更新时间戳
            pooledMapRootView.releaseTime = System.currentTimeMillis()
        } else {
            Log.w(
                TAG,
                "Trying to release a map root view that is not our singleton, destroying it, id: ${pooledMapRootView.id}"
            )
            scheduleMapRootViewDestruction(pooledMapRootView)
        }

        val endTime = System.currentTimeMillis()
        Log.d(
            TAG,
            "releaseMapRootView completed in ${endTime - startTime}ms, id: ${pooledMapRootView.id}"
        )
        Log.d(
            TAG,
            "After release - Active: ${activeMapRootViews.size}, Pooled: ${mapRootViewPool.size}"
        )

        // 安排清理任务
        scheduleCleanup()
    }

    /**
     * 暂停地图组件
     */
    private fun pauseMapComponents(pooledMapRootView: PooledMapRootView) {
        try {
            Log.d(TAG, "Pausing map components for id: ${pooledMapRootView.id}")

            // 暂停地图组件但保持生命周期
            pooledMapRootView.virtualMap?.onPause()
            pooledMapRootView.virtualNavigationMap?.onPause()

        } catch (e: Exception) {
            Log.e(TAG, "Failed to pause map components for id: ${pooledMapRootView.id}", e)
        }
    }

    /**
     * 销毁地图组件
     */
    private fun destroyMapComponents(pooledMapRootView: PooledMapRootView) {
        try {
            Log.d(TAG, "Destroying map components for id: ${pooledMapRootView.id}")

            // 先暂停
            pooledMapRootView.virtualMap?.onPause()
            pooledMapRootView.virtualNavigationMap?.onPause()

            // 再销毁
            pooledMapRootView.virtualMap?.destroy()
            pooledMapRootView.virtualNavigationMap?.destroy()

        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy map components for id: ${pooledMapRootView.id}", e)
        }
    }

    /**
     * 创建新的地图根视图
     */
    private fun createNewMapRootView(context: Context): View {
        val inflater = LayoutInflater.from(context)
        return inflater.inflate(R.layout.root_virtual_main, null, false)
    }

    /**
     * 重置地图根视图状态
     */
    private fun resetMapRootView(rootView: View, isFirstTimeCreation: Boolean = true) {
        // 重置容器可见性
        rootView.findViewById<FrameLayout>(R.id.map_container)?.let { container ->
            container.visibility = View.VISIBLE
        }

        rootView.findViewById<FrameLayout>(R.id.navi_container)?.let { container ->
            container.visibility = View.GONE
        }

        // 阴影处理：第一次创建时显示阴影，复用时直接隐藏
        rootView.findViewById<View>(R.id.v_map_shadow)?.let { shadow ->
            shadow.visibility = if (isFirstTimeCreation) View.VISIBLE else View.GONE
        }

        // 清理动态UI
        rootView.findViewById<FrameLayout>(R.id.navi_ui_container)?.removeAllViews()
    }

    /**
     * 清理地图根视图
     */
    private fun cleanupMapRootView(rootView: View) {
        // 只清理动态添加的UI组件，不移除布局中固定的地图组件
        rootView.findViewById<FrameLayout>(R.id.navi_ui_container)?.removeAllViews()

        // 注意：不清理map_container和navi_container中的内容
        // 因为VirtualMap和VirtualNaviMap是布局文件中固定的组件，不应该被移除
    }

    /**
     * 安排清理任务
     */
    private fun scheduleCleanup() {
        cleanupRunnable?.let { mainHandler.removeCallbacks(it) }

        cleanupRunnable = Runnable {
            performIdleCleanup()
        }

        mainHandler.postDelayed(cleanupRunnable!!, IDLE_CLEANUP_DELAY)
    }

    /**
     * 执行空闲清理
     */
    @Synchronized
    private fun performIdleCleanup() {
        val currentTime = System.currentTimeMillis()

        Log.d(TAG, "Performing idle cleanup")

        // 清理空闲的根视图
        val mapRootViewsToDestroy = mutableListOf<PooledMapRootView>()
        for (pooledMapRootView in mapRootViewPool) {
            if (currentTime - pooledMapRootView.releaseTime > IDLE_CLEANUP_DELAY) {
                mapRootViewsToDestroy.add(pooledMapRootView)
            }
        }

        mapRootViewsToDestroy.forEach { pooledMapRootView ->
            mapRootViewPool.remove(pooledMapRootView)
            scheduleMapRootViewDestruction(pooledMapRootView)
        }

        Log.d(TAG, "Idle cleanup completed. Destroyed ${mapRootViewsToDestroy.size} map root views")
    }

    /**
     * 安排地图根视图延迟销毁
     */
    private fun scheduleMapRootViewDestruction(pooledMapRootView: PooledMapRootView) {
        mainHandler.postDelayed({
            Log.d(TAG, "Destroying map root view id: ${pooledMapRootView.id}")
            try {
                // 销毁地图组件
                destroyMapComponents(pooledMapRootView)

                // 清理视图状态
                cleanupMapRootView(pooledMapRootView.rootView)
            } catch (e: Exception) {
                Log.e(TAG, "Error destroying map root view id: ${pooledMapRootView.id}", e)
            }
        }, 1000) // 1秒延迟，避免阻塞
    }

    /**
     * 强制清理所有资源
     */
    @Synchronized
    fun destroyAllResources() {
        Log.d(TAG, "Force destroy all resources")

        // 清理运行中的任务
        cleanupRunnable?.let { mainHandler.removeCallbacks(it) }

        // 立即销毁所有活跃的资源（不延迟）
        // 注意：activeMapRootViews现在存储的是ID，需要处理单例的销毁
        if (activeMapRootViews.isNotEmpty() && singletonMapRootView != null) {
            try {
                destroyMapComponents(singletonMapRootView!!)
                cleanupMapRootView(singletonMapRootView!!.rootView)
            } catch (e: Exception) {
                Log.e(
                    TAG,
                    "Error force destroying active map root view id: ${singletonMapRootView!!.id}",
                    e
                )
            }
        }
        activeMapRootViews.clear()

        // 销毁单例根视图
        singletonMapRootView?.let { pooledMapRootView ->
            try {
                Log.d(TAG, "Destroying singleton map root view id: ${pooledMapRootView.id}")
                destroyMapComponents(pooledMapRootView)
                cleanupMapRootView(pooledMapRootView.rootView)
            } catch (e: Exception) {
                Log.e(
                    TAG,
                    "Error force destroying singleton map root view id: ${pooledMapRootView.id}",
                    e
                )
            }
        }
        singletonMapRootView = null

        // 清理池（应该已经为空，但为了保险）
        mapRootViewPool.clear()
    }

    /**
     * 获取统计信息
     */
    fun getStatistics(): String {
        return "MapResourcePoolManager Statistics:\n" +
                "Created Map Root Views: ${createdMapRootViewCount.get()}\n" +
                "Active Map Root Views: ${activeMapRootViews.size}\n" +
                "Singleton Map Root View: ${if (singletonMapRootView != null) "exists (id: ${singletonMapRootView!!.id})" else "null"}\n" +
                "Pooled Map Root Views: ${mapRootViewPool.size}"
    }

    /**
     * 调试方法：打印当前资源池状态
     */
    fun printDebugState() {
        Log.d(TAG, "=== MapResourcePoolManager Debug State ===")
        Log.d(TAG, getStatistics())
        Log.d(TAG, "Active Map Root Views IDs: $activeMapRootViews")
        Log.d(
            TAG,
            "Singleton Map Root View: ${singletonMapRootView?.let { "id: ${it.id}, initialized: ${it.isMapInitialized}/${it.isNavigationMapInitialized}" } ?: "null"}")
        Log.d(TAG, "=== End Debug State ===")
    }
}

/**
 * 池化的地图根视图
 */
internal data class PooledMapRootView(
    val id: Int,
    val rootView: View,
    val virtualMap: VirtualMap?,
    val virtualNavigationMap: VirtualNaviMap?,
    val createdTime: Long,
    var acquireTime: Long = 0,
    var releaseTime: Long = 0,
    var isMapInitialized: Boolean = false,
    var isNavigationMapInitialized: Boolean = false
) 