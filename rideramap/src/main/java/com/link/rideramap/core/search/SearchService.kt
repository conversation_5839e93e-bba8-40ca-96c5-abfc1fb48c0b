package com.link.rideramap.core.search

import android.content.Context
import com.link.rideramap.api.dto.PoiAddress
import com.link.rideramap.api.dto.SearchResult
import com.link.rideramap.core.location.domain.entity.LocationInfo

/**
 * 搜索服务接口 - 定义搜索相关功能的契约
 */
interface SearchService {
    
    /**
     * 地理编码搜索 - 根据经纬度获取地址信息
     */
    suspend fun geocodeSearch(
        context: Context,
        latitude: Double,
        longitude: Double
    ): LocationInfo?
    
    /**
     * 关键字搜索
     */
    suspend fun searchByKeyword(
        context: Context,
        keyword: String,
        currentLocation: LocationInfo
    ): SearchResult
    
    /**
     * POI搜索
     */
    suspend fun searchPOI(
        context: Context,
        searchAddress: PoiAddress,
        currentLocation: LocationInfo
    ): SearchResult
} 