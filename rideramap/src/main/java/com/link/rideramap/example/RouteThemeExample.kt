package com.link.rideramap.example

import android.content.Context
import android.graphics.Color
import androidx.core.content.ContextCompat
import com.link.rideramap.R
import com.link.rideramap.api.dto.RiderAMapRouteViewConfig
import com.link.rideramap.presentation.component.route.RiderAMapRouteView

/**
 * RiderAMapRouteView 主题配置使用示例
 * 
 * 展示如何使用新的主题配置系统来自定义 RiderAMapRouteView 的外观
 */
class RouteThemeExample {

    /**
     * 创建日间主题配置
     */
    fun createDayTheme(context: Context): RiderAMapRouteViewConfig {
        return RiderAMapRouteViewConfig(
            // 背景配置
            routeSelectBackgroundRes = R.drawable.route_select_background,
            startEndBackgroundRes = R.drawable.start_end_background,
            
            // 导航按钮配置
            naviButtonBackgroundRes = R.drawable.bt_map_navi_background,
            naviButtonTextColor = ContextCompat.getColor(context, R.color.blue_tx),
            naviButtonText = "开始导航",
            
            // 返回按钮配置
            backButtonIconRes = R.drawable.btn_map_back,
            
            // 文字颜色配置
            routeTimeTextColor = Color.parseColor("#191919"),
            routeDistanceTextColor = Color.parseColor("#191919"),
            routeSelectedColor = ContextCompat.getColor(context, R.color.blue_tx),
            
            // 导航图标配置
            trafficOnIconRes = R.drawable.road_condition_on,
            trafficOffIconRes = R.drawable.road_condition_off,
            mapTypeAutoIconRes = R.drawable.rb_auto_type,
            mapTypeDayIconRes = R.drawable.rb_day_type,
            mapTypeNightIconRes = R.drawable.rb_night_type,
            overviewModeIconRes = R.drawable.rb_overview_navi,
            lockModeIconRes = R.drawable.rb_lock_navi,
            exitNaviIconRes = R.drawable.cancel_navi,
            
            // 导航UI背景配置
            naviRightBarBackgroundRes = R.drawable.navi_right_bar_background,
            naviBottomBarBackgroundRes = R.drawable.navi_bar_background,
            
            // 导航文字颜色配置
            naviTimeTextColor = ContextCompat.getColor(context, R.color.blue_tx),
            naviDistanceTextColor = ContextCompat.getColor(context, R.color.blue_tx),
            
            // 地图配置
            mapShadowColor = ContextCompat.getColor(context, R.color.ramap_map_shadow_color),
            mapType = com.amap.api.maps.AMap.MAP_TYPE_NORMAL
        )
    }

    /**
     * 创建夜间主题配置
     */
    fun createNightTheme(context: Context): RiderAMapRouteViewConfig {
        return RiderAMapRouteViewConfig(
            // 背景配置 - 使用夜间版本
            routeSelectBackgroundRes = R.drawable.route_select_background, // 可以创建夜间版本
            startEndBackgroundRes = R.drawable.start_end_background,
            
            // 导航按钮配置
            naviButtonBackgroundRes = R.drawable.bt_map_navi_background_night,
            naviButtonTextColor = Color.WHITE,
            naviButtonText = "开始导航",
            
            // 返回按钮配置
            backButtonIconRes = R.drawable.btn_map_back, // 可以创建夜间版本
            
            // 文字颜色配置
            routeTimeTextColor = Color.WHITE,
            routeDistanceTextColor = Color.WHITE,
            routeSelectedColor = Color.WHITE,
            
            // 导航图标配置 - 使用夜间版本
            trafficOnIconRes = R.drawable.road_condition_on, // 夜间版本
            trafficOffIconRes = R.drawable.road_condition_off, // 夜间版本
            mapTypeAutoIconRes = R.drawable.rb_auto_type,
            mapTypeDayIconRes = R.drawable.rb_day_type,
            mapTypeNightIconRes = R.drawable.rb_night_type,
            overviewModeIconRes = R.drawable.rb_overview_navi,
            lockModeIconRes = R.drawable.rb_lock_navi,
            exitNaviIconRes = R.drawable.cancel_navi,
            
            // 导航UI背景配置 - 使用夜间版本
            naviRightBarBackgroundRes = R.drawable.navi_right_bar_background_night,
            naviBottomBarBackgroundRes = R.drawable.navi_bar_background, // 可以创建夜间版本
            
            // 导航文字颜色配置
            naviTimeTextColor = Color.WHITE,
            naviDistanceTextColor = Color.WHITE,
            
            // 地图配置
            mapShadowColor = Color.parseColor("#2A2A2A"),
            mapType = com.amap.api.maps.AMap.MAP_TYPE_NIGHT
        )
    }

    /**
     * 应用主题到 RiderAMapRouteView
     */
    fun applyTheme(riderAMapRouteView: RiderAMapRouteView, context: Context, isNightMode: Boolean) {
        val themeConfig = if (isNightMode) {
            createNightTheme(context)
        } else {
            createDayTheme(context)
        }
        
        riderAMapRouteView.updateTheme(themeConfig)
    }

    /**
     * 创建自定义主题配置
     */
    fun createCustomTheme(context: Context): RiderAMapRouteViewConfig {
        return RiderAMapRouteViewConfig(
            // 自定义颜色配置
            naviButtonTextColor = Color.parseColor("#FF6B35"), // 橙色
            routeSelectedColor = Color.parseColor("#FF6B35"),
            naviTimeTextColor = Color.parseColor("#FF6B35"),
            naviDistanceTextColor = Color.parseColor("#FF6B35"),
            
            // 自定义文字
            naviButtonText = "开始骑行",
            
            // 其他配置使用默认值
            mapType = com.amap.api.maps.AMap.MAP_TYPE_NORMAL
        )
    }

    /**
     * 使用统一颜色主题系统的示例
     */
    fun demonstrateUnifiedColorTheme(riderAMapRouteView: RiderAMapRouteView) {
        // 1. 使用预设的日间主题
        val dayTheme = RiderAMapRouteViewConfig.createDayTheme().copy(
            naviButtonText = "开始导航"
        )
        riderAMapRouteView.updateTheme(dayTheme)

        // 2. 使用预设的夜间主题
        val nightTheme = RiderAMapRouteViewConfig.createNightTheme().copy(
            naviButtonText = "开始导航"
        )
        riderAMapRouteView.updateTheme(nightTheme)

        // 3. 使用品牌主题
        val brandColor = Color.parseColor("#FF6B35") // 橙色品牌色
        val brandTheme = RiderAMapRouteViewConfig.createBrandTheme(brandColor, isNightMode = false)
        riderAMapRouteView.updateTheme(brandTheme)

        // 4. 自定义统一主题
        val customTheme = RiderAMapRouteViewConfig(
            primaryColor = Color.parseColor("#FF6B35"),        // 主题色：橙色
            primaryTextColor = Color.parseColor("#191919"),    // 主要文字：深色
            secondaryTextColor = Color.parseColor("#666666"),  // 次要文字：灰色
            naviButtonText = "立即出发"
        )
        riderAMapRouteView.updateTheme(customTheme)
    }
}
