package com.link.rideramap.example

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.link.rideramap.R
import com.link.rideramap.presentation.component.search.*
import kotlinx.coroutines.launch

/**
 * RiderSearchView 使用示例
 * 
 * 展示如何使用重构后的搜索组件
 */
class RiderSearchViewUsageExample : AppCompatActivity() {

    private lateinit var searchView: RiderSearchView
    private lateinit var searchViewModel: SearchViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_search_example)

        initSearchView()
        setupSearchViewModel()
        demonstrateUsage()
    }

    /**
     * 初始化搜索视图
     */
    private fun initSearchView() {
        searchView = findViewById(R.id.rider_search_view)
        
        // 设置生命周期拥有者
        searchView.setLifecycleOwner(this)
        
        // 设置搜索回调
        searchView.setSearchCallback(object : RiderSearchView.SearchCallback {
            override fun onSearchTextChanged(query: String) {
                // 实时搜索
                searchViewModel.search(query)
            }

            override fun onSearchSubmitted(query: String) {
                // 搜索提交
                Toast.makeText(this@RiderSearchViewUsageExample, "搜索: $query", Toast.LENGTH_SHORT).show()
                val address = SearchAddress(name = query, type = 0)
                searchViewModel.addHistory(address)
            }

            override fun onAddressSelected(address: SearchAddress) {
                // 选择搜索结果
                Toast.makeText(this@RiderSearchViewUsageExample, "选择: ${address.name}", Toast.LENGTH_SHORT).show()
                searchViewModel.addHistory(address)
            }

            override fun onHistoryAdded(address: SearchAddress) {
                // 历史记录添加
                searchViewModel.addHistory(address)
            }

            override fun onHistoryDeleted(address: SearchAddress) {
                // 历史记录删除
                searchViewModel.deleteHistory(address)
            }

            override fun onBackPressed() {
                // 返回按钮点击
                finish()
            }

            override fun onCancelPressed() {
                // 取消按钮点击
                searchView.clearSearchText()
            }
        })
    }

    /**
     * 设置搜索 ViewModel
     */
    private fun setupSearchViewModel() {
        searchViewModel = SearchViewModelFactory(this).create()
        
        // 观察搜索结果
        lifecycleScope.launch {
            searchViewModel.searchResults.collect { results ->
                searchView.showSearchResults(results)
            }
        }
        
        // 观察历史记录
        lifecycleScope.launch {
            searchViewModel.histories.collect { histories ->
                searchView.showHistories(histories)
            }
        }
        
        // 观察错误状态
        lifecycleScope.launch {
            searchViewModel.error.collect { error ->
                error?.let {
                    Toast.makeText(this@RiderSearchViewUsageExample, it, Toast.LENGTH_SHORT).show()
                    searchViewModel.clearError()
                }
            }
        }
    }

    /**
     * 演示各种使用方式
     */
    private fun demonstrateUsage() {
        // 1. 基础配置
        demonstrateBasicConfiguration()
        
        // 2. 主题配置
        demonstrateThemeConfiguration()
        
        // 3. 高级配置
        demonstrateAdvancedConfiguration()
        
        // 4. 动态配置更新
        demonstrateDynamicConfiguration()
    }

    /**
     * 演示基础配置
     */
    private fun demonstrateBasicConfiguration() {
        val basicConfig = RiderSearchViewConfig.createDefault()
        searchView.updateConfig(basicConfig)
    }

    /**
     * 演示主题配置
     */
    private fun demonstrateThemeConfiguration() {
        // 深色主题
        val darkConfig = RiderSearchViewConfig.createDarkTheme()
        
        // 浅色主题
        val lightConfig = RiderSearchViewConfig.createLightTheme()
        
        // 可以根据系统主题切换
        val config = if (isDarkTheme()) darkConfig else lightConfig
        searchView.updateConfig(config)
    }

    /**
     * 演示高级配置
     */
    private fun demonstrateAdvancedConfiguration() {
        val advancedConfig = RiderSearchViewConfig.Builder()
            .searchHint("请输入地点名称")
            .searchTextColor(Color.BLACK)
            .searchHintColor(Color.GRAY)
            .backgroundColor(Color.WHITE)
            .highlightColor(Color.BLUE)
            .showHistory(true)
            .maxHistoryCount(15)
            .debounceTime(500L)
            .enableHighlight(true)
            .highlightIndividualChars(false)
            .build()
        
        searchView.updateConfig(advancedConfig)
    }

    /**
     * 演示动态配置更新
     */
    private fun demonstrateDynamicConfiguration() {
        // 可以在运行时动态更新配置
        val currentConfig = RiderSearchViewConfig.createDefault()
        
        // 例如：根据用户设置更新最大历史记录数
        val updatedConfig = currentConfig.copy(maxHistoryCount = 20)
        searchView.updateConfig(updatedConfig)
        
        // 例如：切换历史记录显示
        val noHistoryConfig = currentConfig.copy(showHistory = false)
        // searchView.updateConfig(noHistoryConfig)
    }

    /**
     * 检查是否为深色主题
     */
    private fun isDarkTheme(): Boolean {
        val nightModeFlags = resources.configuration.uiMode and 
            android.content.res.Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == android.content.res.Configuration.UI_MODE_NIGHT_YES
    }
}

/**
 * 在对话框中使用搜索组件的示例
 */
class SearchDialogUsageExample {
    
    fun showSearchDialog(context: Context) {
        // 创建自定义配置
        val config = RiderSearchViewConfig.Builder()
            .searchHint("搜索地点")
            .showHistory(true)
            .maxHistoryCount(10)
            .build()
        
        // 创建并显示对话框
        val dialog = RefactoredSearchDialogFragment.newInstance(
            onAddressSelected = { address ->
                // 处理选择的地址
                handleAddressSelection(address)
            },
            config = config
        )
        
        if (context is androidx.fragment.app.FragmentActivity) {
            dialog.show(context.supportFragmentManager, "search_dialog")
        }
    }
    
    private fun handleAddressSelection(address: SearchAddress) {
        // 处理地址选择逻辑
        println("Selected address: ${address.name}")
    }
}

/**
 * 在 XML 中使用搜索组件的示例
 */
/*
XML 布局示例：

<com.link.rideramap.presentation.component.search.RiderSearchView
    android:id="@+id/rider_search_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:search_hint="请输入搜索内容"
    app:search_text_color="#202229"
    app:search_hint_color="#999999"
    app:search_background_color="#FFFFFF"
    app:search_highlight_color="#5C7BD7"
    app:show_history="true"
    app:max_history_count="10"
    app:enable_highlight="true"
    app:highlight_individual_chars="false" />
*/
