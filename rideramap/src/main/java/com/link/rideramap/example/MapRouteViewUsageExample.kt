package com.link.rideramap.example

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.link.rideramap.R
import com.link.rideramap.api.dto.RiderAMapRouteViewConfig
import com.link.rideramap.presentation.component.route.RiderAMapRouteView

/**
 * RiderAMapRouteView 使用示例
 * 
 * 展示重构后的 RiderAMapRouteView 的完整使用方法
 */
class MapRouteViewUsageExample : AppCompatActivity() {

    private lateinit var riderAMapRouteView: RiderAMapRouteView
    private var isNightMode = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化 RiderAMapRouteView
        initMapRouteView()
        
        // 设置初始主题
        applyInitialTheme()
        
        // 设置主题切换监听
        setupThemeToggle()
    }

    /**
     * 初始化 RiderAMapRouteView
     */
    private fun initMapRouteView() {
        riderAMapRouteView = RiderAMapRouteView(this)
        
        // 设置布局参数等...
        // setContentView(riderAMapRouteView)
    }

    /**
     * 应用初始主题
     */
    private fun applyInitialTheme() {
        // 根据系统设置或用户偏好确定初始主题
        isNightMode = isSystemInNightMode()
        
        val initialTheme = if (isNightMode) {
            createNightTheme()
        } else {
            createDayTheme()
        }
        
        riderAMapRouteView.updateTheme(initialTheme)
    }

    /**
     * 创建日间主题
     */
    private fun createDayTheme(): RiderAMapRouteViewConfig {
        return RiderAMapRouteViewConfig(
            // 背景配置
            routeSelectBackgroundRes = R.drawable.route_select_background,
            startEndBackgroundRes = R.drawable.start_end_background,
            
            // 导航按钮配置
            naviButtonBackgroundRes = R.drawable.bt_map_navi_background,
            naviButtonTextColor = ContextCompat.getColor(this, R.color.blue_tx),
            naviButtonText = getString(R.string.start_navigation),
            
            // 返回按钮配置
            backButtonIconRes = R.drawable.btn_map_back,
            
            // 文字颜色配置
            routeTimeTextColor = Color.parseColor("#191919"),
            routeDistanceTextColor = Color.parseColor("#191919"),
            routeSelectedColor = ContextCompat.getColor(this, R.color.blue_tx),
            
            // 导航图标配置
            trafficOnIconRes = R.drawable.road_condition_on,
            trafficOffIconRes = R.drawable.road_condition_off,
            mapTypeAutoIconRes = R.drawable.rb_auto_type,
            mapTypeDayIconRes = R.drawable.rb_day_type,
            mapTypeNightIconRes = R.drawable.rb_night_type,
            overviewModeIconRes = R.drawable.rb_overview_navi,
            lockModeIconRes = R.drawable.rb_lock_navi,
            exitNaviIconRes = R.drawable.cancel_navi,
            
            // 导航UI背景配置
            naviRightBarBackgroundRes = R.drawable.navi_right_bar_background,
            naviBottomBarBackgroundRes = R.drawable.navi_bar_background,
            
            // 导航文字颜色配置
            naviTimeTextColor = ContextCompat.getColor(this, R.color.blue_tx),
            naviDistanceTextColor = ContextCompat.getColor(this, R.color.blue_tx),
            
            // 地图配置
            mapShadowColor = ContextCompat.getColor(this, R.color.ramap_map_shadow_color),
            mapType = com.amap.api.maps.AMap.MAP_TYPE_NORMAL
        )
    }

    /**
     * 创建夜间主题
     */
    private fun createNightTheme(): RiderAMapRouteViewConfig {
        return RiderAMapRouteViewConfig(
            // 背景配置
            routeSelectBackgroundRes = R.drawable.route_select_background,
            startEndBackgroundRes = R.drawable.start_end_background,
            
            // 导航按钮配置
            naviButtonBackgroundRes = R.drawable.bt_map_navi_background_night,
            naviButtonTextColor = Color.WHITE,
            naviButtonText = getString(R.string.start_navigation),
            
            // 返回按钮配置
            backButtonIconRes = R.drawable.btn_map_back,
            
            // 文字颜色配置
            routeTimeTextColor = Color.WHITE,
            routeDistanceTextColor = Color.WHITE,
            routeSelectedColor = Color.WHITE,
            
            // 导航图标配置
            trafficOnIconRes = R.drawable.road_condition_on,
            trafficOffIconRes = R.drawable.road_condition_off,
            mapTypeAutoIconRes = R.drawable.rb_auto_type,
            mapTypeDayIconRes = R.drawable.rb_day_type,
            mapTypeNightIconRes = R.drawable.rb_night_type,
            overviewModeIconRes = R.drawable.rb_overview_navi,
            lockModeIconRes = R.drawable.rb_lock_navi,
            exitNaviIconRes = R.drawable.cancel_navi,
            
            // 导航UI背景配置
            naviRightBarBackgroundRes = R.drawable.navi_right_bar_background_night,
            naviBottomBarBackgroundRes = R.drawable.navi_bar_background,
            
            // 导航文字颜色配置
            naviTimeTextColor = Color.WHITE,
            naviDistanceTextColor = Color.WHITE,
            
            // 地图配置
            mapShadowColor = Color.parseColor("#2A2A2A"),
            mapType = com.amap.api.maps.AMap.MAP_TYPE_NIGHT
        )
    }

    /**
     * 创建自定义品牌主题
     */
    private fun createBrandTheme(): RiderAMapRouteViewConfig {
        val brandColor = Color.parseColor("#FF6B35") // 品牌橙色
        
        return RiderAMapRouteViewConfig(
            // 使用品牌色
            naviButtonTextColor = brandColor,
            routeSelectedColor = brandColor,
            naviTimeTextColor = brandColor,
            naviDistanceTextColor = brandColor,
            
            // 自定义文字
            naviButtonText = "开始骑行",
            
            // 其他配置使用默认值
            mapType = com.amap.api.maps.AMap.MAP_TYPE_NORMAL
        )
    }

    /**
     * 设置主题切换
     */
    private fun setupThemeToggle() {
        // 示例：点击某个按钮切换主题
        // themeToggleButton.setOnClickListener {
        //     toggleTheme()
        // }
    }

    /**
     * 切换主题
     */
    private fun toggleTheme() {
        isNightMode = !isNightMode
        
        val newTheme = if (isNightMode) {
            createNightTheme()
        } else {
            createDayTheme()
        }
        
        // 应用新主题
        riderAMapRouteView.updateTheme(newTheme)
        
        // 保存用户偏好
        saveThemePreference(isNightMode)
    }

    /**
     * 应用品牌主题
     */
    private fun applyBrandTheme() {
        val brandTheme = createBrandTheme()
        riderAMapRouteView.updateTheme(brandTheme)
    }

    /**
     * 动态更新特定配置
     */
    private fun updateSpecificConfig() {
        // 只更新特定的配置项
        val partialConfig = RiderAMapRouteViewConfig(
            naviButtonText = "立即出发",
            naviButtonTextColor = Color.parseColor("#00BCD4")
        )
        
        riderAMapRouteView.updateTheme(partialConfig)
    }

    /**
     * 检查系统是否处于夜间模式
     */
    private fun isSystemInNightMode(): Boolean {
        val nightModeFlags = resources.configuration.uiMode and 
                android.content.res.Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == android.content.res.Configuration.UI_MODE_NIGHT_YES
    }

    /**
     * 保存主题偏好
     */
    private fun saveThemePreference(isNightMode: Boolean) {
        val prefs = getSharedPreferences("theme_prefs", Context.MODE_PRIVATE)
        prefs.edit().putBoolean("is_night_mode", isNightMode).apply()
    }

    /**
     * 加载主题偏好
     */
    private fun loadThemePreference(): Boolean {
        val prefs = getSharedPreferences("theme_prefs", Context.MODE_PRIVATE)
        return prefs.getBoolean("is_night_mode", false)
    }

    /**
     * 演示 RouteButtonView 主题配置
     */
    private fun demonstrateRouteButtonTheme() {
        // 创建专门的 RouteButtonView 主题配置
        val routeButtonTheme = RiderAMapRouteViewConfig(
            routeButtonSelectedTextColor = Color.parseColor("#FF6B35"),
            routeButtonUnselectedTextColor = Color.parseColor("#666666"),
            routeButtonSelectedBackgroundRes = R.drawable.gpsmode_bg_s,
            routeButtonTrafficLightSelectedIconRes = R.drawable.traffic_light_num,
            routeButtonTrafficLightUnselectedIconRes = R.drawable.traffic_light_num_idle
        )

        riderAMapRouteView.updateTheme(routeButtonTheme)
    }

    /**
     * 演示配置合并
     */
    private fun demonstrateConfigurationMerge() {
        // 基础配置
        val baseConfig = createDayTheme()

        // 覆盖配置
        val overrideConfig = RiderAMapRouteViewConfig(
            naviButtonText = "立即出发",
            routeButtonSelectedTextColor = Color.parseColor("#FF6B35")
        )

        // 合并配置
        val finalConfig = RiderAMapRouteViewConfig.merge(overrideConfig, baseConfig)
        riderAMapRouteView.updateTheme(finalConfig)
    }
}
