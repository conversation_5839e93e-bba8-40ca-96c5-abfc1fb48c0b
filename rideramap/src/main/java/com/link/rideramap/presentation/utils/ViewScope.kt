package com.link.rideramap.presentation.utils

import android.util.Log
import android.view.View
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.cancellation.CancellationException

class ViewScope(
    private val view: View,
    private val dispatcher: CoroutineDispatcher = Dispatchers.Main,
) : CoroutineScope {
    private val tag = ViewScope::class.java.simpleName
    private var currentJob: Job? = null

    override val coroutineContext: CoroutineContext
        get() {
            if (currentJob == null || currentJob?.isActive == false){
                currentJob = SupervisorJob()
            }
            return dispatcher + currentJob!!
        }

    val isActive: Boolean
        get() = view.isAttachedToWindow

    init {
        view.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
                Log.d(tag, "ViewScope for $view attached. Scope is now active.")
            }

            override fun onViewDetachedFromWindow(v: View) {
                cancelScope("View $view detached from window.")
            }

        })
    }

    fun cancelScope(message: String) {
        currentJob?.cancel(CancellationException(message))
        // Nullify the job so a new one can be created if the view is re-attached.
        currentJob = null
        Log.d(tag, "ViewScope for $view cancelled: $message")
    }
}