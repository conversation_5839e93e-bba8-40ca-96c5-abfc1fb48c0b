package com.link.rideramap.presentation.display.base

import android.app.Presentation
import android.content.Context
import android.view.Display
import android.view.View

/**
 * <AUTHOR>
 * @date 2022/6/29
 */
abstract class MirrorBasePresentation @JvmOverloads constructor(
    outerContext: Context,
    display: Display,
    theme: Int = 0
) : Presentation(outerContext, display, theme) {

    abstract fun getRootView(): View?

    abstract fun changeMap(type: Int)
}