package com.link.rideramap.presentation.component.search

import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Context
import android.database.sqlite.SQLiteDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.withContext
import com.link.rideramap.presentation.component.search.UserHistoryContract.HistoryEntry
import kotlinx.coroutines.flow.Flow

class UserHistoryDao(context: Context) {

    private val dbHelper = SdkDatabaseHelper.getInstance(context)

    // 1. 使用 MutableStateFlow 来模拟 Room 的响应式查询
    private val _historiesFlow = MutableStateFlow<List<SearchAddress>>(emptyList())

    init {
        // 初始化时加载一次数据
        refreshHistories()
    }

    // 2. 对外暴露为不可变的 Flow
    fun loadHistories(): Flow<List<SearchAddress>> = _historiesFlow

    /**
     * 手动触发数据刷新，并通知 Flow 更新
     */
    private fun refreshHistories() {
        val db = dbHelper.readableDatabase
        val cursor = db.query(
            HistoryEntry.TABLE_NAME,
            null, // all columns
            null, // selection
            null, // selectionArgs
            null, // groupBy
            null, // having
            "${HistoryEntry.COLUMN_NAME_ID} DESC" // orderBy
        )

        val histories = mutableListOf<SearchAddress>()
        cursor.use { c ->
            while (c.moveToNext()) {
                histories.add(mapCursorToSearchAddress(c))
            }
        }
        // 发出新的列表
        _historiesFlow.value = histories
    }

    suspend fun insertHistory(searchAddress: SearchAddress) = withContext(Dispatchers.IO) {
        val db = dbHelper.writableDatabase
        val values = ContentValues().apply {
            put(HistoryEntry.COLUMN_NAME_AD_CODE, searchAddress.ad_code)
            put(HistoryEntry.COLUMN_NAME_DISTRICT, searchAddress.district)
            put(HistoryEntry.COLUMN_NAME_NAME, searchAddress.name)
            put(HistoryEntry.COLUMN_NAME_POI_ID, searchAddress.poi_id)
            put(HistoryEntry.COLUMN_NAME_POINT_LATITUDE, searchAddress.point_latitude)
            put(HistoryEntry.COLUMN_NAME_POINT_LONGITUDE, searchAddress.point_longitude)
            put(HistoryEntry.COLUMN_NAME_TYPE, searchAddress.type)
        }

        // 使用 CONFLICT_REPLACE 策略，如果主键冲突则替换旧数据
        db.insertWithOnConflict(
            HistoryEntry.TABLE_NAME,
            null,
            values,
            SQLiteDatabase.CONFLICT_REPLACE
        )

        // 3. 操作完成后，刷新 Flow
        refreshHistories()
    }

    suspend fun deleteHistory(searchAddress: SearchAddress) = withContext(Dispatchers.IO) {
        val db = dbHelper.writableDatabase
        val selection = "${HistoryEntry.COLUMN_NAME_ID} = ?"
        val selectionArgs = arrayOf(searchAddress.id.toString())

        db.delete(HistoryEntry.TABLE_NAME, selection, selectionArgs)

        // 3. 操作完成后，刷新 Flow
        refreshHistories()
    }

    // 一个辅助方法，将 Cursor 的当前行映射为 SearchAddress 对象
    @SuppressLint("Range")
    private fun mapCursorToSearchAddress(cursor: android.database.Cursor): SearchAddress {
        return SearchAddress(
            id = cursor.getInt(cursor.getColumnIndex(HistoryEntry.COLUMN_NAME_ID)),
            ad_code = cursor.getString(cursor.getColumnIndex(HistoryEntry.COLUMN_NAME_AD_CODE)),
            district = cursor.getString(cursor.getColumnIndex(HistoryEntry.COLUMN_NAME_DISTRICT)),
            name = cursor.getString(cursor.getColumnIndex(HistoryEntry.COLUMN_NAME_NAME)),
            poi_id = cursor.getString(cursor.getColumnIndex(HistoryEntry.COLUMN_NAME_POI_ID)),
            point_latitude = cursor.getString(cursor.getColumnIndex(HistoryEntry.COLUMN_NAME_POINT_LATITUDE)),
            point_longitude = cursor.getString(cursor.getColumnIndex(HistoryEntry.COLUMN_NAME_POINT_LONGITUDE)),
            type = cursor.getInt(cursor.getColumnIndex(HistoryEntry.COLUMN_NAME_TYPE))
        )
    }
}