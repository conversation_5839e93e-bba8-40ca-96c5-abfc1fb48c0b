package com.link.rideramap.presentation.component.navigation

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import com.link.rideramap.api.dto.NavigationInfo

abstract class NaviView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    /**
     * 通知导航数据改变
     * @param navigationInfo 导航数据
     * @see NavigationInfo
     */
    abstract fun onNaviDataChanged(navigationInfo: NavigationInfo)

    /**
     * 通知 GPS 信号强度改变
     * @param isWeek GPS 信号强度
     */
    abstract fun onGpsSignalWeak(isWeek: Boolean)
}