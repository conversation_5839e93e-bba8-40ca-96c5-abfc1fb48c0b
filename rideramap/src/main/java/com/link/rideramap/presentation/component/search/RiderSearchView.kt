package com.link.rideramap.presentation.component.search

import android.content.Context
import android.os.Build
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.FrameLayout
import androidx.appcompat.app.AlertDialog
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import com.link.rideramap.R
import com.link.rideramap.api.SPRiderAMap
import com.link.rideramap.databinding.RiderSearchViewBinding
import com.link.rideramap.presentation.utils.AutoSizeUtils
import com.link.rideramap.presentation.utils.ViewScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.launch
import java.util.regex.Pattern

/**
 * 独立的搜索视图组件
 *
 * 提供以下核心功能：
 * - 搜索输入和实时搜索
 * - 搜索历史记录管理
 * - 搜索结果展示
 * - 关键字高亮显示
 * - 主题切换支持
 * - 键盘管理
 */
class RiderSearchView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "RiderSearchView"
        private const val SEARCH_DEBOUNCE_TIME = 300L
        private const val HIGHLIGHT_COLOR = "#5C7BD7"
    }

    private val historyDao by lazy { UserHistoryDao(context) }


    // 数据绑定
    private val binding: RiderSearchViewBinding =
        RiderSearchViewBinding.inflate(LayoutInflater.from(context), this, true)

    // 适配器
    private val historyAdapter = ViewRecyclerAdapter()
    private val searchAdapter = ViewRecyclerAdapter()

    // 配置和回调
    private var config: RiderSearchViewConfig = RiderSearchViewConfig()
    private var callback: SearchCallback? = null
    private var lifecycleOwner: LifecycleOwner? = null

    // 内部状态
    private var keyword: String = ""
    private val viewScope = ViewScope(this)

    init {
        AutoSizeUtils.updateResources(context)
        loadAttributesFromXml(attrs, defStyleAttr)
        setupUI()
        setupListeners()
        render()
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        if (visibility == VISIBLE) {
            showSoftInput()
        } else {
            hideSoftInput()
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        loadHistories()
    }

    private fun loadHistories(){
        viewScope.launch {
            historyDao.loadHistories().collect {
                showHistories(it)
            }
        }
    }

    /**
     * 从 XML 属性加载配置
     */
    private fun loadAttributesFromXml(attrs: AttributeSet?, defStyleAttr: Int) {
        attrs ?: return
        val typedArray = context.theme.obtainStyledAttributes(
            attrs, R.styleable.RiderSearchView, defStyleAttr, 0
        )

        try {
            // 加载各种配置属性
            config = config.copy(
                searchHint = typedArray.getString(R.styleable.RiderSearchView_search_hint)
                    ?: config.searchHint,
                searchTextColor = typedArray.getColor(
                    R.styleable.RiderSearchView_search_text_color,
                    config.searchTextColor
                ),
                searchHintColor = typedArray.getColor(
                    R.styleable.RiderSearchView_search_hint_color,
                    config.searchHintColor
                ),
                backgroundColor = typedArray.getColor(
                    R.styleable.RiderSearchView_search_background_color,
                    config.backgroundColor
                ),
                highlightColor = typedArray.getColor(
                    R.styleable.RiderSearchView_search_highlight_color,
                    config.highlightColor
                ),
                showHistory = typedArray.getBoolean(
                    R.styleable.RiderSearchView_show_history,
                    config.showHistory
                ),
                maxHistoryCount = typedArray.getInt(
                    R.styleable.RiderSearchView_max_history_count,
                    config.maxHistoryCount
                )
            )
        } finally {
            typedArray.recycle()
        }
    }

    /**
     * 设置UI初始状态
     */
    private fun setupUI() {
        binding.apply {
            // 设置搜索框
            tvAddressName.apply {
                setText("")
                hint = config.searchHint
                setTextColor(config.searchTextColor)
                setHintTextColor(config.searchHintColor)
            }

            // 设置历史记录列表
            historyView.apply {
                layoutManager = LinearLayoutManager(context)
                adapter = historyAdapter
                visibility = if (config.showHistory) View.VISIBLE else View.GONE
            }

            // 设置搜索结果列表
            searchResultView.apply {
                layoutManager = LinearLayoutManager(context)
                adapter = searchAdapter
            }

            // 设置背景
            searchDialogBackgroundImg.setBackgroundColor(config.backgroundColor)
        }
    }

    /**
     * 设置事件监听器
     */
    @OptIn(FlowPreview::class, ExperimentalCoroutinesApi::class)
    private fun setupListeners() {
        binding.apply {
            // 返回按钮
            ibBack.setOnClickListener {
                hideSoftInput()
                callback?.onBackPressed()
            }

            // 取消按钮
            searchCancel.setOnClickListener {
                hideSoftInput()
                callback?.onCancelPressed()
            }

            // 搜索框按键监听
            searchBox.setOnKeyListener { _, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_UP) {
                    val trimmedText = tvAddressName.text.toString().trim()
                    if (!TextUtils.isEmpty(trimmedText)) {
                        hideSoftInput()
                        addToHistory(SearchAddress(name = trimmedText, type = 0))
                        return@setOnKeyListener true
                    }
                }
                false
            }
        }

        // 设置文本变化监听
        setupTextChangeListener()
    }

    /**
     * 设置文本变化监听器
     */
    @OptIn(FlowPreview::class, ExperimentalCoroutinesApi::class)
    private fun setupTextChangeListener() {
        viewScope.launch {
            try {
                binding.tvAddressName.textChangeFlow()
                    .filter { text ->
                        val isNotEmpty = text.isNotEmpty()
                        binding.viewAnimator.displayedChild = if (isNotEmpty) 1 else 0
                        if (!isNotEmpty) {
                            clearSearchResults()
                        }
                        isNotEmpty
                    }
                    .debounce(SEARCH_DEBOUNCE_TIME)
                    .mapLatest { newKeyword ->
                        keyword = newKeyword.toString()
                        search(keyword)
                    }
                    .collect {}
            }catch (e: Exception){
                e.printStackTrace()
            }
        }
    }


    private fun search(keyword: String) {
        viewScope.launch {
            val result = SPRiderAMap.instance.search(keyword).searchList?.map {
                SearchAddress(
                    ad_code = it.adCode,
                    district = it.district,
                    name = it.name,
                    poi_id = it.poiId,
                    point_latitude = it.latitude.toString(),
                    point_longitude = it.longitude.toString(),
                )
            } ?: emptyList()
            showSearchResults(result)
        }
    }

    /**
     * 渲染视图
     */
    private fun render() {
        binding.apply {
            // 应用当前配置
            tvAddressName.apply {
                hint = config.searchHint
                setTextColor(config.searchTextColor)
                setHintTextColor(config.searchHintColor)
            }

            searchDialogBackgroundImg.setBackgroundColor(config.backgroundColor)

            // 更新历史记录可见性
            historyView.visibility = if (config.showHistory) View.VISIBLE else View.GONE
        }
    }

    /**
     * 设置搜索回调
     */
    fun setSearchCallback(callback: SearchCallback) {
        this.callback = callback
    }

    /**
     * 更新配置
     */
    fun updateConfig(newConfig: RiderSearchViewConfig) {
        this.config = newConfig
        render()
    }

    /**
     * 显示搜索结果
     */
    private fun showSearchResults(results: List<SearchAddress>) {
        searchAdapter.clear()
        results.forEach { address ->
            searchAdapter.add(SearchResultViewBinder(address))
        }
    }

    /**
     * 显示历史记录
     */
    private fun showHistories(histories: List<SearchAddress>) {
        if (!config.showHistory) return

        historyAdapter.clear()
        binding.historyView.visibility = if (histories.isEmpty()) GONE else VISIBLE
        histories.take(config.maxHistoryCount).forEach { address ->
            historyAdapter.add(HistoryViewBinder(address))
        }
    }

    /**
     * 清除搜索结果
     */
    private fun clearSearchResults() {
        searchAdapter.clear()
    }

    /**
     * 添加到历史记录
     */
    private fun addToHistory(address: SearchAddress) {
        viewScope.launch {
            historyDao.insertHistory(address)
        }
    }

    /**
     * 显示软键盘
     */
    private fun showSoftInput() {
        binding.tvAddressName.apply {
            isFocusable = true
            isFocusableInTouchMode = true
            requestFocus()
        }

        if (Build.VERSION.SDK_INT >= 30) {
            val windowInsetsController = WindowInsetsControllerCompat(
                (context as? android.app.Activity)?.window ?: return,
                binding.tvAddressName
            )
            windowInsetsController.show(WindowInsetsCompat.Type.ime())
        } else {
            val inputMethodManager =
                context.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
            inputMethodManager?.showSoftInput(
                binding.tvAddressName,
                InputMethodManager.SHOW_IMPLICIT
            )
        }
    }

    /**
     * 隐藏软键盘
     */
    private fun hideSoftInput() {
        if (Build.VERSION.SDK_INT >= 30) {
            val windowInsetsController = WindowInsetsControllerCompat(
                (context as? android.app.Activity)?.window ?: return,
                binding.tvAddressName
            )
            windowInsetsController.hide(WindowInsetsCompat.Type.ime())
        } else {
            val inputMethodManager =
                context.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
            inputMethodManager?.hideSoftInputFromWindow(binding.tvAddressName.windowToken, 0)
        }
    }

    /**
     * 关键字高亮显示
     */
    private fun stringToHighLight(
        text: String,
        highlightIndividualChars: Boolean = false
    ): SpannableStringBuilder {
        val spannable = SpannableStringBuilder(text)
        if (keyword.isEmpty()) return spannable

        try {
            val keywordList =
                if (highlightIndividualChars) keyword.map { it.toString() } else listOf(keyword)

            keywordList.forEach { singleKeyword ->
                val escapedKeyword = Pattern.quote(singleKeyword)
                val pattern = Pattern.compile("(?i)$escapedKeyword")
                val matcher = pattern.matcher(text)

                while (matcher.find()) {
                    spannable.setSpan(
                        ForegroundColorSpan(config.highlightColor),
                        matcher.start(),
                        matcher.end(),
                        Spannable.SPAN_MARK_MARK
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "stringToHighLight-Error: ${e.message}")
        }

        return spannable
    }

    /**
     * 显示删除确认对话框
     */
    private fun showDeleteConfirmDialog(addressToDelete: SearchAddress?) {
        AlertDialog.Builder(context).apply {
            setTitle("提示")
            setMessage("是否删除这条记录")
            setPositiveButton("确定") { _, _ ->
                addressToDelete?.let {
                    viewScope.launch {
                        try {
                            historyDao.deleteHistory(it)
                        }catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
            }
            setNegativeButton("取消") { _, _ -> }
        }.create().show()
    }

    /**
     * 历史记录ViewBinder
     */
    inner class HistoryViewBinder(private val address: SearchAddress) :
        ViewBinder<SearchAddress>(address) {

        override fun onCreateView(
            recyclerViewHolder: RecyclerViewHolder,
            addressData: SearchAddress
        ) {
            setupCommonViewProperties(recyclerViewHolder, addressData)
            recyclerViewHolder.setImageResource(R.id.iv_type, R.drawable.item_history)

            recyclerViewHolder.itemView.setOnLongClickListener {
                showDeleteConfirmDialog(addressData)
                true
            }
        }

        override val binderLayout: Int
            get() = R.layout.search_item
    }

    /**
     * 搜索结果ViewBinder
     */
    inner class SearchResultViewBinder(private val address: SearchAddress) :
        ViewBinder<SearchAddress>(address) {

        override val binderLayout: Int
            get() = R.layout.search_item

        override fun onCreateView(
            recyclerViewHolder: RecyclerViewHolder,
            addressData: SearchAddress
        ) {
            setupCommonViewProperties(recyclerViewHolder, addressData)
            recyclerViewHolder.setImageResource(R.id.iv_type, R.drawable.item_location)
        }
    }

    /**
     * 设置共同的ViewHolder属性
     */
    private fun setupCommonViewProperties(
        recyclerViewHolder: RecyclerViewHolder,
        addressData: SearchAddress
    ) {
        recyclerViewHolder.run {
            setText(R.id.tv_address, addressData.name)

            setImageResource(
                R.id.img_go_route,
                SearchThemeManager.getCurrentThemeRes(context, R.drawable.item_route)
            )

            setTextColor(
                R.id.tv_address,
                SearchThemeColors.SEARCH_TEXT.getCurrentColorInt(context)
            )

            setTextSpan(
                R.id.tv_address,
                stringToHighLight(addressData.name)
            )

            itemView.debounceClick {
                callback?.onAddressSelected(addressData)
                hideSoftInput()
                addToHistory(addressData)
            }
        }
    }

    /**
     * 搜索回调接口
     */
    interface SearchCallback {
        /**
         * 搜索结果选择
         */
        fun onAddressSelected(address: SearchAddress)

        /**
         * 返回按钮点击
         */
        fun onBackPressed()

        /**
         * 取消按钮点击
         */
        fun onCancelPressed()
    }
}
