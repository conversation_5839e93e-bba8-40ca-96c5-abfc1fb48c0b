package com.link.rideramap.presentation.component.lock

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.link.rideramap.R
import com.link.rideramap.databinding.LockScreenBinding

internal class LockView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    init {
        LayoutInflater.from(context).inflate(R.layout.lock_screen, this, true)
    }
}