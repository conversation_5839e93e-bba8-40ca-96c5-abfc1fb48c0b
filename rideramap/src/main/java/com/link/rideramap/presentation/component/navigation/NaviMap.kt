package com.link.rideramap.presentation.component.navigation

import android.content.Context
import android.os.Bundle
import android.util.AttributeSet
import com.amap.api.maps.AMap
import com.amap.api.maps.CustomRenderer
import com.amap.api.navi.AMapNaviView
import com.link.rideramap.presentation.component.listener.IActionListener
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10

class NaviMap @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : AMapNaviView(context, attrs) {
    private lateinit var aMap: AMap
    private var actionListener: IActionListener? = null


    private val mapRenderer = object : CustomRenderer {
        override fun onSurfaceCreated(openGl: GL10?, eglConfig: EGLConfig?) {
            aMap.setCustomRenderer(null)
            post {
                actionListener?.onVisible()
            }
        }

        override fun onSurfaceChanged(openGl: GL10?, width: Int, height: Int) {

        }

        override fun onDrawFrame(openGl: GL10?) {

        }

        override fun OnMapReferencechanged() {

        }

    }

    fun create(bundle: Bundle?) {
        super.onCreate(bundle)
        aMap = map
        aMap.setCustomRenderer(mapRenderer)
    }

    fun destroy() {
        super.onDestroy()
        aMap.setCustomRenderer(null)
    }

    fun setActionListener(listener: IActionListener) {
        actionListener = listener
    }
}