package com.link.rideramap.virtual

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import com.link.rideramap.R
import com.link.rideramap.databinding.LayerNaviLandBinding
import com.link.rideramap.common.ext.formatStepRetainDistance
import com.link.rideramap.common.ext.formatTime
import com.link.rideramap.api.dto.NavigationInfo
import com.link.rideramap.common.ext.getDistance
import com.link.rideramap.common.ext.getTime
import com.link.rideramap.core.navigation.utils.NaviUtil
import com.link.rideramap.presentation.component.navigation.VirtualNaviView

/**
 * <AUTHOR>
 * @date 2022/6/29
 */

internal class VirtualNaviLandView constructor(context: Context) : VirtualNaviView(context) {
    private val binding = LayerNaviLandBinding.inflate(LayoutInflater.from(context), this, true)

    @SuppressLint("SetTextI18n")
    override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
        binding.ivNaviIcon.setImageResource(NaviUtil.AMapNaviIcon.getResId(navigationInfo.iconType))

        binding.tvNaviDistance.text = navigationInfo.getDistance()
        binding.tvNaviRemaintime.text = navigationInfo.getTime()
        binding.tvNaviTraffic.text = navigationInfo.routeRemainLightCount.toString()
        binding.tvNaviTime.text = navigationInfo.formatTime()

        binding.tvDirection.text = navigationInfo.turnIconName
        binding.tvNextRoad.text = "进入"+navigationInfo.nextRoadName
        binding.tvNextRoadDistance.text = navigationInfo.formatStepRetainDistance()
    }

    override fun onGpsSignalWeak(isWeek: Boolean) {
        binding.tvGpsSignal.text = if (isWeek) context.getString(R.string.gps_signal_week) else ""
    }

    companion object {
        private const val TAG = "VirtualHomeView"
    }
}