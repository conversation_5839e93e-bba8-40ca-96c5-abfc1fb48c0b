package com.link.rideramap.presentation.component.route

import android.content.Context
import android.graphics.Color
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.res.ResourcesCompat
import com.link.rideramap.R
import com.link.rideramap.databinding.RouteViewBinding
import com.link.rideramap.common.ext.formatDistance
import com.link.rideramap.common.ext.formatTime
import com.link.rideramap.core.search.domain.entity.RouteData
import com.link.rideramap.api.dto.RiderAMapRouteViewConfig
import java.text.SimpleDateFormat
import java.util.*
import androidx.core.content.ContextCompat

internal class RouteButtonView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : ConstraintLayout(context, attrs, defStyle) {
    private val binding: RouteViewBinding =
        RouteViewBinding.inflate(LayoutInflater.from(context), this, true)

    private var themeConfig: RiderAMapRouteViewConfig? = null
    private var isNight = false

    // 默认配置
    private val defaultSelectedBackgroundRes = R.drawable.gpsmode_bg_s
    private val defaultSelectedBackgroundNightRes = R.drawable.gpsmode_bg_s_night
    private val defaultSelectedTextColor = Color.parseColor("#5C7BD7")
    private val defaultUnselectedTextColor = Color.parseColor("#202229")
    private val defaultUnselectedTextColorNight = Color.parseColor("#FFFFFF")
    private val defaultTrafficLightSelectedIconRes = R.drawable.traffic_light_num
    private val defaultTrafficLightUnselectedIconRes = R.drawable.traffic_light_num_idle
    private val defaultTrafficLightUnselectedIconNightRes = R.drawable.traffic_light_num_idle_night
    private fun format(total: Int, textView: TextView) {
        val minutes = String.format("%s 分钟", total)
        SpannableStringBuilder(minutes).also {
            it.setSpan(
                48,
                0,
                minutes.length - 4,
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            )
            it.setSpan(
                48,
                minutes.length - 4,
                minutes.length,
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            )
            textView.text = it
        }
    }

    fun setData(title: String, routeData: RouteData, isNight: Boolean) {
        binding.titleTv.text = title
        binding.totalMinutesTv.apply {
            format(routeData.formatTime(), this)
        }
        binding.trafficLightNumTv.apply {
            text = routeData.trafficLights.toString()
        }
        binding.distanceTv.apply {
            text = routeData.formatDistance()
        }
        binding.timeTv.apply {
            text = formatRouteTime(routeData.allTime)
        }
        this.isNight = isNight
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        format(0, binding.totalMinutesTv)
    }


    override fun setSelected(selected: Boolean) {
        super.setSelected(selected)
        applyTheme()
    }

    /**
     * 更新主题配置
     * @param config 主题配置
     */
    fun updateTheme(config: RiderAMapRouteViewConfig) {
        this.themeConfig = config
        applyTheme()
    }

    /**
     * 应用主题配置
     */
    private fun applyTheme() {
        if (isSelected) {
            applySelectedTheme()
        } else {
            applyUnselectedTheme()
        }
    }

    /**
     * 应用选中状态主题
     */
    private fun applySelectedTheme() {
        // 背景
        val selectedBackground = themeConfig?.routeButtonSelectedBackgroundDrawable
            ?: themeConfig?.routeButtonSelectedBackgroundRes?.let { ContextCompat.getDrawable(context, it) }
            ?: ResourcesCompat.getDrawable(resources, selectThemeInt(defaultSelectedBackgroundRes, defaultSelectedBackgroundNightRes), null)
        background = selectedBackground

        // 文字颜色 - 使用统一的颜色获取方法
        val selectedTextColor = themeConfig?.resolveRouteButtonSelectedTextColor() ?: defaultSelectedTextColor
        binding.titleTv.setTextColor(selectedTextColor)
        binding.timeTv.setTextColor(selectedTextColor)
        binding.totalMinutesTv.setTextColor(selectedTextColor)
        binding.trafficLightNumTv.setTextColor(selectedTextColor)
        binding.distanceTv.setTextColor(selectedTextColor)

        // 交通灯图标
        val trafficLightIcon = themeConfig?.routeButtonTrafficLightSelectedIconRes ?: defaultTrafficLightSelectedIconRes
        binding.trafficLightNumImg.setImageResource(trafficLightIcon)
    }

    /**
     * 应用未选中状态主题
     */
    private fun applyUnselectedTheme() {
        // 背景
        val unselectedBackground = themeConfig?.routeButtonUnselectedBackgroundDrawable
            ?: themeConfig?.routeButtonUnselectedBackgroundRes?.let { ContextCompat.getDrawable(context, it) }

        if (unselectedBackground != null) {
            background = unselectedBackground
        } else {
            setBackgroundColor(Color.TRANSPARENT)
        }

        // 文字颜色 - 使用统一的颜色获取方法
        val unselectedTextColor = themeConfig?.resolveRouteButtonUnselectedTextColor()
            ?: if (isNight) defaultUnselectedTextColorNight else defaultUnselectedTextColor
        binding.titleTv.setTextColor(unselectedTextColor)
        binding.timeTv.setTextColor(unselectedTextColor)
        binding.totalMinutesTv.setTextColor(unselectedTextColor)
        binding.trafficLightNumTv.setTextColor(unselectedTextColor)
        binding.distanceTv.setTextColor(unselectedTextColor)

        // 交通灯图标
        val trafficLightIcon = themeConfig?.routeButtonTrafficLightUnselectedIconRes
            ?: selectThemeInt(defaultTrafficLightUnselectedIconRes, defaultTrafficLightUnselectedIconNightRes)
        binding.trafficLightNumImg.setImageResource(trafficLightIcon)
    }

    fun selectThemeInt(day: Int, night: Int): Int{
        return if(isNight){
            night
        }else{
            day
        }
    }
    fun selectThemeString(day: String, night: String): String{
        return if(isNight){
            night
        }else{
            day
        }
    }

    private fun formatRouteTime(time: Int): String {
        val currentTime = System.currentTimeMillis() + time * 1000
        val delegate = "hh:mm aaa"
        val sdf = SimpleDateFormat(delegate, Locale.ENGLISH)
        return sdf.format(currentTime).lowercase(Locale.ENGLISH)
    }

    /**
     * 主题变化回调（保持向后兼容）
     * @param isNight 是否夜间模式
     */
    fun onThemeChange(isNight: Boolean) {
        this.isNight = isNight
        applyTheme()
    }
}




