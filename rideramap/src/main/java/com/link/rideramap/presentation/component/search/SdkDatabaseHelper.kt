package com.link.rideramap.presentation.component.search

import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper

class SdkDatabaseHelper(context: Context) :
    SQLiteOpenHelper(context, DATABASE_NAME, null, DATABASE_VERSION) {

    override fun onCreate(db: SQLiteDatabase) {
        // 创建表的 SQL 语句
        val sqlCreateTable = """
            CREATE TABLE ${UserHistoryContract.HistoryEntry.TABLE_NAME} (
                ${UserHistoryContract.HistoryEntry.COLUMN_NAME_ID} INTEGER PRIMARY KEY AUTOINCREMENT,
                ${UserHistoryContract.HistoryEntry.COLUMN_NAME_AD_CODE} TEXT NOT NULL,
                ${UserHistoryContract.HistoryEntry.COLUMN_NAME_DISTRICT} TEXT NOT NULL,
                ${UserHistoryContract.HistoryEntry.COLUMN_NAME_NAME} TEXT NOT NULL,
                ${UserHistoryContract.HistoryEntry.COLUMN_NAME_POI_ID} TEXT NOT NULL,
                ${UserHistoryContract.HistoryEntry.COLUMN_NAME_POINT_LATITUDE} TEXT NOT NULL,
                ${UserHistoryContract.HistoryEntry.COLUMN_NAME_POINT_LONGITUDE} TEXT NOT NULL,
                ${UserHistoryContract.HistoryEntry.COLUMN_NAME_TYPE} INTEGER NOT NULL
            )
        """.trimIndent()
        db.execSQL(sqlCreateTable)
    }

    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        // 这里的升级策略很简单，直接删除旧表创建新表。
        // 在生产环境中，你可能需要根据版本号进行数据迁移。
        db.execSQL("DROP TABLE IF EXISTS ${UserHistoryContract.HistoryEntry.TABLE_NAME}")
        onCreate(db)
    }

    companion object {
        const val DATABASE_VERSION = 1
        // 为了避免冲突，SDK 的数据库名最好有一个独特的前缀
        const val DATABASE_NAME = "com_link_rideramap_sdk.rider.db"

        // 使用单例模式，避免多个 Helper 实例导致的问题
        @Volatile
        private var INSTANCE: SdkDatabaseHelper? = null

        fun getInstance(context: Context): SdkDatabaseHelper =
            INSTANCE ?: synchronized(this) {
                INSTANCE ?: SdkDatabaseHelper(context.applicationContext).also { INSTANCE = it }
            }
    }
}