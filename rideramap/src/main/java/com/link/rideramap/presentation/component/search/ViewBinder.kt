package com.link.rideramap.presentation.component.search

import androidx.annotation.LayoutRes

abstract class ViewBinder<Any>(private val mData: Any) {
    fun bindView(recyclerViewHolder: RecyclerViewHolder) {
        onCreateView(recyclerViewHolder, mData)
    }

    @get:LayoutRes
    abstract val binderLayout: Int

    abstract fun onCreateView(recyclerViewHolder: RecyclerViewHolder, t: Any)


    override fun hashCode(): Int {
        return mData.hashCode()
    }

    override fun equals(other: kotlin.Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ViewBinder<*>

        return mData == other.mData
    }

}