package com.link.rideramap.presentation.display.manager

import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.Color
import android.os.Bundle
import android.util.DisplayMetrics
import android.util.Log
import android.view.Display
import android.view.View
import android.widget.FrameLayout
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.model.BitmapDescriptorFactory
import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.Marker
import com.amap.api.maps.model.MarkerOptions
import com.amap.api.navi.enums.MapStyle
import com.link.rideramap.R
import com.link.rideramap.api.SPRiderAMap
import com.link.rideramap.api.dto.NavigationInfo
import com.link.rideramap.api.callback.NaviCallback
import com.link.rideramap.core.infrastructure.pool.MapResourcePoolManager
import com.link.rideramap.core.infrastructure.pool.PooledMapRootView
import com.link.rideramap.presentation.component.listener.IActionListener
import com.link.rideramap.virtual.VirtualNaviLandView
import com.link.rideramap.presentation.component.navigation.VirtualNaviPortView
import com.link.rideramap.presentation.component.navigation.VirtualNaviView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

internal class VirtualContentManager(
    private val context: Context,
    private var rootView: View
) {
    // 池化资源
    private var pooledRootView: PooledMapRootView? = null
    
    // 容器视图
    private var mapContainer: FrameLayout? = null
    private var naviContainer: FrameLayout? = null
    private var shadowView: View? = null
    
    // 地图组件
    private var aMap: AMap? = null
    private var marker: Marker? = null
    private var locationJob: Job? = null
    
    // 导航组件
    private var virtualNaviView: VirtualNaviView? = null
    
    // 状态管理
    private var currentContentType = VirtualContentType.MAP
    private var isMapInitialized = false
    private var isNaviInitialized = false

    // 导航数据回调
    private val naviDataCallback = object : NaviCallback() {
        override fun onStartNavi() {
            super.onStartNavi()
            Log.d(TAG, "onStartNavi")
            switchToNavigation()
        }

        override fun onStopNavi() {
            super.onStopNavi()
            Log.d(TAG, "onStopNavi")
            switchToMap()
        }

        override fun onEndEmulatorNavi() {
            super.onEndEmulatorNavi()
            Log.d(TAG, "onEndEmulatorNavi")
            switchToMap()
        }

        override fun onArriveDestination() {
            super.onArriveDestination()
            Log.d(TAG, "onArriveDestination")
            switchToMap()
        }
        
        override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
            super.onNaviDataChanged(navigationInfo)
            virtualNaviView?.onNaviDataChanged(navigationInfo)
        }

        override fun onGpsSignalWeak(isWeak: Boolean) {
            super.onGpsSignalWeak(isWeak)
            virtualNaviView?.onGpsSignalWeak(isWeak)
        }
    }
    
    /**
     * 初始化内容管理器 - 使用资源池
     */
    fun initialize(bundle: Bundle?, display: Display) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "VirtualContentManager initialize start at: $startTime")
        
        try {
            // 注意：pooledRootView应该已经在VirtualMapPresentation中设置好了
            if (pooledRootView == null) {
                throw RuntimeException("PooledRootView not set before initialize")
            }
            
            // 设置初始化状态 - 使用池对象中的准确状态标志
            isMapInitialized = pooledRootView!!.isMapInitialized
            isNaviInitialized = pooledRootView!!.isNavigationMapInitialized
            
            // 初始化容器视图
            initializeContainers()
            
            // 初始化阴影视图
            initializeShadowView()
            
            // 根据当前状态决定优先配置哪个内容
            val isNavi = SPRiderAMap.instance.isNavi()
            currentContentType = if (isNavi) VirtualContentType.NAVIGATION else VirtualContentType.MAP

            // 直接配置所有内容，无需延迟（组件已复用，只需配置）
            if (isMapInitialized) {
                configureMapContent()
            }
            
            if (isNaviInitialized) {
                configureNaviContent(display)
            }

            // 设置初始可见性
            updateContentVisibility()
            
            // 注册导航状态监听
            SPRiderAMap.instance.addNaviDataCallback(naviDataCallback)
            
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "VirtualContentManager initialized in ${endTime - startTime}ms")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize VirtualContentManager", e)
            throw e
        }
    }
    
    /**
     * 设置池化根视图
     */
    fun setPooledRootView(pooledRootView: PooledMapRootView) {
        this.pooledRootView = pooledRootView
        this.rootView = pooledRootView.rootView
    }
    
    /**
     * 配置地图内容
     */
    private fun configureMapContent() {
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Configuring map content at: $startTime")

            // 获取地图实例并配置
            aMap = pooledRootView?.virtualMap?.map?.apply {
                uiSettings.isZoomControlsEnabled = false
                uiSettings.isIndoorSwitchEnabled = false
                uiSettings.isZoomGesturesEnabled = false
                showBuildings(false)
                setRenderFps(15)
                
                // 设置地图类型
                mapType = SPRiderAMap.instance.getDefaultMode()
            }

            // 设置地图可见监听器
            pooledRootView?.virtualMap?.setActionListener(object : IActionListener {
                override fun onVisible() {
                    rootView.findViewById<View>(R.id.v_map_shadow)?.visibility = View.GONE
                }
            })

            // 设置当前位置
            setupLocation()

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Map content configured in ${endTime - startTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to configure map content", e)
        }
    }
    
    /**
     * 配置导航内容
     */
    private fun configureNaviContent(display: Display) {
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Configuring navigation content at: $startTime")
            
            // 配置导航地图选项
            setupNaviViewOptions()

                    // 设置地图可见监听器
        pooledRootView?.virtualNavigationMap?.setActionListener(object : IActionListener {
            override fun onVisible() {
                rootView.findViewById<View>(R.id.v_map_shadow)?.visibility = View.GONE
            }
        })
            
            // 添加导航UI视图
            addVirtualNaviView(display)
            
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Navigation content configured in ${endTime - startTime}ms")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to configure navigation content", e)
        }
    }
    
    /**
     * 初始化容器视图
     */
    private fun initializeContainers() {
        mapContainer = rootView.findViewById(R.id.map_container)
        naviContainer = rootView.findViewById(R.id.navi_container)
        shadowView = rootView.findViewById(R.id.v_map_shadow)
    }
    
    /**
     * 初始化阴影视图
     */
    private fun initializeShadowView() {
        shadowView?.setBackgroundColor(Color.argb(0xFF, 0XEB, 0XEB, 0XEB))
    }
    
    /**
     * 根据屏幕方向添加对应的导航视图
     */
    private fun addVirtualNaviView(display: Display) {
        val displayMetrics = DisplayMetrics()
        display.getMetrics(displayMetrics)
        
        virtualNaviView = if (displayMetrics.widthPixels < displayMetrics.heightPixels) {
            VirtualNaviPortView(context) // 竖屏
        } else {
            VirtualNaviLandView(context) // 横屏
        }
        
        rootView.findViewById<FrameLayout>(R.id.navi_ui_container)?.addView(virtualNaviView)
    }
    
    /**
     * 设置导航视图选项
     */
    private fun setupNaviViewOptions() {
        val mapType = SPRiderAMap.instance.getDefaultModeNavi()
        val viewOptions = pooledRootView?.virtualNavigationMap?.viewOptions?.apply {
            isSettingMenuEnabled = false
            isLayoutVisible = false
            isTrafficBarEnabled = false
            isAutoChangeZoom = false
            setModeCrossDisplayShow(false)
            isRealCrossDisplayShow = false
            isAfterRouteAutoGray = true
            isAutoLockCar = true
            isDrawBackUpOverlay = false
            lockMapDelayed = 7000L
            tilt = 35
            carBitmap = BitmapFactory.decodeResource(context.resources, R.drawable.amap_navmylocation)
            if (mapType == AMap.MAP_TYPE_NIGHT) {
                setMapStyle(MapStyle.NIGHT, null)
            } else {
                setMapStyle(MapStyle.DAY, null)
            }
        }
        pooledRootView?.virtualNavigationMap?.viewOptions = viewOptions
    }
    
    /**
     * 设置位置信息
     */
    private fun setupLocation() {
        // 先尝试使用缓存的位置
        val cachedLocation = SPRiderAMap.instance.getLocation()
        cachedLocation?.let {
            setPosition(it.latitude, it.longitude)
        }
        
        // 异步获取最新位置
        locationJob = MainScope().launch(Dispatchers.IO) {
            try {
                val locationInfo = SPRiderAMap.instance.getLbsLocation()
                setPosition(locationInfo.latitude, locationInfo.longitude)
                SPRiderAMap.instance.setLocation(locationInfo)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get location", e)
            }
        }
    }

    /**
     * 设置地图位置
     */
    private fun setPosition(latitude: Double, longitude: Double) {
        if (!isMapInitialized || aMap == null) return

        val latLng = LatLng(latitude, longitude)
        aMap?.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, 17f))
        updateMarker(latLng)
    }

    /**
     * 更新位置标记
     */
    private fun updateMarker(latLng: LatLng) {
        marker?.remove()

        val markerOptions = MarkerOptions().apply {
            anchor(0.5f, 0.5f)
            isFlat = true
            visible(true)
            zIndex(2.2f)
            position(latLng)

            // 设置标记图标
            val locationBitmap = BitmapFactory.decodeResource(context.resources, R.drawable.nearme)
            icon(BitmapDescriptorFactory.fromBitmap(locationBitmap))
        }

        marker = aMap?.addMarker(markerOptions)
    }

    /**
     * 切换到地图模式
     */
    private fun switchToMap() {
        if (currentContentType == VirtualContentType.MAP) {
            Log.d(TAG, "Already in map mode")
            return
        }

        val startTime = System.currentTimeMillis()
        Log.d(TAG, "Switching to map mode at: $startTime")

        currentContentType = VirtualContentType.MAP
        updateContentVisibility()

        // 恢复地图
        if (isMapInitialized) {
            pooledRootView?.virtualMap?.onResume()
        }

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "Switched to map mode in ${endTime - startTime}ms")
    }

    /**
     * 切换到导航模式
     */
    private fun switchToNavigation() {
        if (currentContentType == VirtualContentType.NAVIGATION) {
            Log.d(TAG, "Already in navigation mode")
            return
        }

        val startTime = System.currentTimeMillis()
        Log.d(TAG, "Switching to navigation mode at: $startTime")

        currentContentType = VirtualContentType.NAVIGATION
        updateContentVisibility()

        // 恢复导航地图
        if (isNaviInitialized) {
            pooledRootView?.virtualNavigationMap?.onResume()
        }

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "Switched to navigation mode in ${endTime - startTime}ms")
    }

    /**
     * 更新内容可见性
     */
    private fun updateContentVisibility() {
        when (currentContentType) {
            VirtualContentType.MAP -> {
                mapContainer?.visibility = View.VISIBLE
                naviContainer?.visibility = View.GONE
            }
            VirtualContentType.NAVIGATION -> {
                mapContainer?.visibility = View.GONE
                naviContainer?.visibility = View.VISIBLE
            }
        }
    }

    /**
     * 恢复内容
     */
    fun onResume() {
        when (currentContentType) {
            VirtualContentType.MAP -> {
                if (isMapInitialized) {
                    pooledRootView?.virtualMap?.onResume()
                }
            }
            VirtualContentType.NAVIGATION -> {
                if (isNaviInitialized) {
                    pooledRootView?.virtualNavigationMap?.onResume()
                }
            }
        }
    }

    /**
     * 更改地图类型
     */
    fun changeMapType(mapType: Int) {
        // 更新地图类型
        if (isMapInitialized) {
            aMap?.mapType = mapType
        }

        // 更新导航地图类型
        if (isNaviInitialized) {
            val options = pooledRootView?.virtualNavigationMap?.viewOptions?.apply {
                if (mapType == AMap.MAP_TYPE_NIGHT) {
                    setMapStyle(MapStyle.NIGHT, null)
                } else {
                    setMapStyle(MapStyle.DAY, null)
                }
            }
            pooledRootView?.virtualNavigationMap?.viewOptions = options
        }

        Log.d(TAG, "Map type changed to: $mapType")
    }

    /**
     * 轻量级清理 - 归还资源到池中
     */
    fun cleanup() {
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Starting VirtualContentManager cleanup at: $startTime")

            // 移除导航状态监听，防止内存泄漏
            SPRiderAMap.instance.removeNaviDataCallback(naviDataCallback)

            // 立即清理关键资源
            locationJob?.cancel()
            marker?.remove()
            virtualNaviView = null

            // 归还资源到池中而不是销毁
            returnResourcesToPool()

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "VirtualContentManager cleanup completed in ${endTime - startTime}ms")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup VirtualContentManager", e)
        }
    }
    
    /**
     * 归还资源到池中
     */
    private fun returnResourcesToPool() {
        // 归还地图根视图
        pooledRootView?.let { 
            MapResourcePoolManager.releaseMapRootView(it)
            pooledRootView = null
        }
        
        Log.d(TAG, "Resources returned to pool")
    }

    /**
     * 立即强制销毁（用于应用退出等场景）
     */
    fun destroy() {
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "Starting immediate VirtualContentManager destroy at: $startTime")

            // 移除导航状态监听
            SPRiderAMap.instance.removeNaviDataCallback(naviDataCallback)

            // 同步销毁所有资源
            locationJob?.cancel()
            marker?.remove()

            // 强制清理资源池（仅在应用退出时使用）
            MapResourcePoolManager.destroyAllResources()

            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Immediate VirtualContentManager destroy completed in ${endTime - startTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy VirtualContentManager", e)
        }
    }

    companion object {
        private const val TAG = "VirtualContentManager"
    }
}

enum class VirtualContentType {
    MAP,        // 普通地图
    NAVIGATION  // 导航地图
}
