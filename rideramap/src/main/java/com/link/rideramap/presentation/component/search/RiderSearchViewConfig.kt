package com.link.rideramap.presentation.component.search

import android.graphics.Color
import androidx.annotation.ColorInt
import androidx.annotation.StyleRes

/**
 * RiderSearchView 配置类
 * 
 * 用于配置搜索组件的外观和行为
 */
data class RiderSearchViewConfig(
    /**
     * 搜索提示文本
     */
    val searchHint: String = "请输入搜索内容",

    /**
     * 搜索文本颜色
     */
    @ColorInt
    val searchTextColor: Int = Color.parseColor("#202229"),

    /**
     * 搜索提示文本颜色
     */
    @ColorInt
    val searchHintColor: Int = Color.parseColor("#999999"),

    /**
     * 背景颜色
     */
    @ColorInt
    val backgroundColor: Int = Color.WHITE,

    /**
     * 关键字高亮颜色
     */
    @ColorInt
    val highlightColor: Int = Color.parseColor("#5C7BD7"),

    /**
     * 是否显示历史记录
     */
    val showHistory: Boolean = true,

    /**
     * 最大历史记录数量
     */
    val maxHistoryCount: Int = 10,

    /**
     * 搜索防抖时间（毫秒）
     */
    val debounceTime: Long = 300L,

    /**
     * 是否启用关键字高亮
     */
    val enableHighlight: Boolean = true,

    /**
     * 是否启用个别字符高亮（分词高亮）
     */
    val highlightIndividualChars: Boolean = false,

    /**
     * 返回按钮图标资源ID
     */
    val backButtonIcon: Int? = null,

    /**
     * 取消按钮图标资源ID
     */
    val cancelButtonIcon: Int? = null,

    /**
     * 搜索框背景资源ID
     */
    val searchBoxBackground: Int? = null,

    /**
     * 历史记录图标资源ID
     */
    val historyIcon: Int? = null,

    /**
     * 搜索结果图标资源ID
     */
    val searchResultIcon: Int? = null,

    /**
     * 路线按钮图标资源ID
     */
    val routeButtonIcon: Int? = null,

    /**
     * 自定义样式资源ID
     */
    @StyleRes
    val customStyle: Int? = null
) {

    companion object {
        /**
         * 创建默认配置
         */
        fun createDefault(): RiderSearchViewConfig {
            return RiderSearchViewConfig()
        }

        /**
         * 创建深色主题配置
         */
        fun createDarkTheme(): RiderSearchViewConfig {
            return RiderSearchViewConfig(
                searchTextColor = Color.WHITE,
                searchHintColor = Color.parseColor("#CCCCCC"),
                backgroundColor = Color.parseColor("#1E1E1E"),
                highlightColor = Color.parseColor("#4A90E2")
            )
        }

        /**
         * 创建浅色主题配置
         */
        fun createLightTheme(): RiderSearchViewConfig {
            return RiderSearchViewConfig(
                searchTextColor = Color.parseColor("#202229"),
                searchHintColor = Color.parseColor("#999999"),
                backgroundColor = Color.WHITE,
                highlightColor = Color.parseColor("#5C7BD7")
            )
        }

        /**
         * 创建简洁模式配置（不显示历史记录）
         */
        fun createSimpleMode(): RiderSearchViewConfig {
            return RiderSearchViewConfig(
                showHistory = false,
                maxHistoryCount = 0
            )
        }

        /**
         * 创建高性能配置（减少动画和效果）
         */
        fun createPerformanceMode(): RiderSearchViewConfig {
            return RiderSearchViewConfig(
                enableHighlight = false,
                debounceTime = 500L,
                maxHistoryCount = 5
            )
        }
    }

    /**
     * 构建器模式
     */
    class Builder {
        private var searchHint: String = "请输入搜索内容"
        private var searchTextColor: Int = Color.parseColor("#202229")
        private var searchHintColor: Int = Color.parseColor("#999999")
        private var backgroundColor: Int = Color.WHITE
        private var highlightColor: Int = Color.parseColor("#5C7BD7")
        private var showHistory: Boolean = true
        private var maxHistoryCount: Int = 10
        private var debounceTime: Long = 300L
        private var enableHighlight: Boolean = true
        private var highlightIndividualChars: Boolean = false
        private var backButtonIcon: Int? = null
        private var cancelButtonIcon: Int? = null
        private var searchBoxBackground: Int? = null
        private var historyIcon: Int? = null
        private var searchResultIcon: Int? = null
        private var routeButtonIcon: Int? = null
        private var customStyle: Int? = null

        fun searchHint(hint: String) = apply { this.searchHint = hint }
        fun searchTextColor(@ColorInt color: Int) = apply { this.searchTextColor = color }
        fun searchHintColor(@ColorInt color: Int) = apply { this.searchHintColor = color }
        fun backgroundColor(@ColorInt color: Int) = apply { this.backgroundColor = color }
        fun highlightColor(@ColorInt color: Int) = apply { this.highlightColor = color }
        fun showHistory(show: Boolean) = apply { this.showHistory = show }
        fun maxHistoryCount(count: Int) = apply { this.maxHistoryCount = count }
        fun debounceTime(time: Long) = apply { this.debounceTime = time }
        fun enableHighlight(enable: Boolean) = apply { this.enableHighlight = enable }
        fun highlightIndividualChars(enable: Boolean) = apply { this.highlightIndividualChars = enable }
        fun backButtonIcon(iconRes: Int) = apply { this.backButtonIcon = iconRes }
        fun cancelButtonIcon(iconRes: Int) = apply { this.cancelButtonIcon = iconRes }
        fun searchBoxBackground(bgRes: Int) = apply { this.searchBoxBackground = bgRes }
        fun historyIcon(iconRes: Int) = apply { this.historyIcon = iconRes }
        fun searchResultIcon(iconRes: Int) = apply { this.searchResultIcon = iconRes }
        fun routeButtonIcon(iconRes: Int) = apply { this.routeButtonIcon = iconRes }
        fun customStyle(@StyleRes styleRes: Int) = apply { this.customStyle = styleRes }

        fun build(): RiderSearchViewConfig {
            return RiderSearchViewConfig(
                searchHint = searchHint,
                searchTextColor = searchTextColor,
                searchHintColor = searchHintColor,
                backgroundColor = backgroundColor,
                highlightColor = highlightColor,
                showHistory = showHistory,
                maxHistoryCount = maxHistoryCount,
                debounceTime = debounceTime,
                enableHighlight = enableHighlight,
                highlightIndividualChars = highlightIndividualChars,
                backButtonIcon = backButtonIcon,
                cancelButtonIcon = cancelButtonIcon,
                searchBoxBackground = searchBoxBackground,
                historyIcon = historyIcon,
                searchResultIcon = searchResultIcon,
                routeButtonIcon = routeButtonIcon,
                customStyle = customStyle
            )
        }
    }

    /**
     * 合并配置
     * 
     * @param other 要合并的配置，其非空值会覆盖当前配置
     * @return 合并后的新配置
     */
    fun merge(other: RiderSearchViewConfig): RiderSearchViewConfig {
        return RiderSearchViewConfig(
            searchHint = other.searchHint.takeIf { it.isNotEmpty() } ?: this.searchHint,
            searchTextColor = other.searchTextColor,
            searchHintColor = other.searchHintColor,
            backgroundColor = other.backgroundColor,
            highlightColor = other.highlightColor,
            showHistory = other.showHistory,
            maxHistoryCount = other.maxHistoryCount,
            debounceTime = other.debounceTime,
            enableHighlight = other.enableHighlight,
            highlightIndividualChars = other.highlightIndividualChars,
            backButtonIcon = other.backButtonIcon ?: this.backButtonIcon,
            cancelButtonIcon = other.cancelButtonIcon ?: this.cancelButtonIcon,
            searchBoxBackground = other.searchBoxBackground ?: this.searchBoxBackground,
            historyIcon = other.historyIcon ?: this.historyIcon,
            searchResultIcon = other.searchResultIcon ?: this.searchResultIcon,
            routeButtonIcon = other.routeButtonIcon ?: this.routeButtonIcon,
            customStyle = other.customStyle ?: this.customStyle
        )
    }

    /**
     * 验证配置的有效性
     */
    fun validate(): Boolean {
        return maxHistoryCount >= 0 && debounceTime >= 0
    }
}
