package com.link.rideramap.presentation.display.impl

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.Display
import android.view.View
import android.view.WindowManager
import com.link.rideramap.core.infrastructure.pool.MapResourcePoolManager
import com.link.rideramap.core.infrastructure.pool.PooledMapRootView
import com.link.rideramap.presentation.display.base.MirrorBasePresentation
import com.link.rideramap.presentation.display.manager.VirtualContentManager

internal class MapMirrorPresentation @JvmOverloads constructor(
    context: Context,
    display: Display,
    theme: Int = 0,
    val isSupportCircularScreen: Boolean
) : MirrorBasePresentation(context, display, theme) {
    private var contentManager: VirtualContentManager? = null
    private var pooledRootView: PooledMapRootView? = null

    public override fun onCreate(savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        super.onCreate(savedInstanceState)
        Log.d(TAG, "MapMirrorPresentation onCreate start at: $startTime")

        // 从资源池获取地图根视图
        pooledRootView = MapResourcePoolManager.acquireMapRootView(context, savedInstanceState)
        setContentView(pooledRootView!!.rootView)
        val layoutTime = System.currentTimeMillis()
        Log.d(TAG, "Map root view obtained from pool in ${layoutTime - startTime}ms, id: ${pooledRootView!!.id}")

        // 创建内容管理器并设置池化根视图
        val initStartTime = System.currentTimeMillis()
        contentManager = VirtualContentManager(context, pooledRootView!!.rootView)
        contentManager?.setPooledRootView(pooledRootView!!)
        contentManager?.initialize(savedInstanceState, display)
        val initEndTime = System.currentTimeMillis()
        Log.d(TAG, "VirtualContentManager initialized in ${initEndTime - initStartTime}ms")

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "MapMirrorPresentation onCreate completed in ${endTime - startTime}ms")

        // 调试信息
        MapResourcePoolManager.printDebugState()
    }

    override fun onStart() {
        super.onStart()
        contentManager?.onResume()
    }

    override fun onStop() {
        super.onStop()
        // 暂停时不需要特殊处理，保持当前状态
    }

    override fun show() {
        super.show()
        Log.d(TAG, "show screen")
    }

    override fun getRootView(): View? {
        return pooledRootView?.rootView
    }

    override fun changeMap(type: Int) {
        contentManager?.changeMapType(type)
        Log.d(TAG, "changeMap: $type")
    }

    override fun dismiss() {
        val startTime = System.currentTimeMillis()
        super.dismiss()
        Log.d(TAG, "dismiss in - start time: $startTime")

        // 使用资源池清理策略，避免阻塞dismiss
        contentManager?.cleanup()
        contentManager = null

        // 资源已经归还到池中，这里不需要额外处理
        pooledRootView = null

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "dismiss screen completed in ${endTime - startTime}ms")

        // 调试信息
        MapResourcePoolManager.printDebugState()
    }

    companion object {
        private const val TAG = "MapMirrorPresentation"
    }

    init {
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        window?.addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
        window?.addFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
        window?.addFlags(WindowManager.LayoutParams.FLAG_LOCAL_FOCUS_MODE)
        window?.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        window?.setType(WindowManager.LayoutParams.TYPE_PRIVATE_PRESENTATION)
        window?.clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
    }
}