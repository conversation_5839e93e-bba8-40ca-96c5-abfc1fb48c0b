package com.link.rideramap.presentation.component.map

import android.content.Context
import android.os.Bundle
import android.util.AttributeSet
import androidx.lifecycle.Transformations.map
import com.amap.api.maps.AMap
import com.amap.api.maps.CustomRenderer
import com.amap.api.maps.TextureMapView
import com.link.rideramap.presentation.component.listener.IActionListener
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10


internal class VirtualMap @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, theme: Int = 0,
) : TextureMapView(context, attrs, theme) {
    private var actionListener: IActionListener? = null
    private lateinit var aMap: AMap
    private val customRender = object : CustomRenderer {
        override fun onSurfaceCreated(gl: GL10?, config: EGLConfig?) {
            aMap.setCustomRenderer(null)
            post {
                actionListener?.onVisible()
            }
        }

        override fun onSurfaceChanged(gl: GL10?, width: Int, height: Int) {

        }

        override fun onDrawFrame(gl: GL10?) {

        }

        override fun OnMapReferencechanged() {

        }

    }

    fun create(bundle: Bundle?) {
        super.onCreate(bundle)
        aMap = map
        aMap.isTrafficEnabled = true
        aMap.setCustomRenderer(customRender)
    }

    fun destroy() {
        try {
            super.onDestroy()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        aMap.setCustomRenderer(null)
        aMap.clear()
    }

    fun setActionListener(listener: IActionListener) {
        actionListener = listener
    }

    companion object {
        private const val TAG = "VirtualMap"
    }
}