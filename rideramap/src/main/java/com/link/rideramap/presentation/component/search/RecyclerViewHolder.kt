package com.link.rideramap.presentation.component.search

import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.IdRes
import androidx.recyclerview.widget.RecyclerView

class RecyclerViewHolder private constructor(view: View) : RecyclerView.ViewHolder(view) {
    private val mViewMap: MutableMap<Int, View> = mutableMapOf()

    private inline fun <reified T : View> getItemView(@IdRes id: Int): T {
        var view = mViewMap[id]
        if (view == null) {
            view = itemView.findViewById<T>(id)
            mViewMap[id] = view
        }

        return view as T
    }

    fun setText(@IdRes id: Int, str: String?) {
        getItemView<TextView>(id).text = str
    }

    fun setTextColor(@IdRes id: Int, color: Int) {
        getItemView<TextView>(id).setTextColor(color)
    }

    fun setTextSpan(@IdRes id: Int, str: SpannableStringBuilder?) {
        getItemView<TextView>(id).text = str
    }

    fun setVisibility(@IdRes id: Int, visibility: Int) {
        getItemView<View>(id).visibility = visibility
    }

    fun setImageResource(@IdRes id: Int, resId: Int) {
        getItemView<ImageView>(id).setImageResource(resId)
    }

    companion object {
        operator fun get(view: View): RecyclerViewHolder {
            return RecyclerViewHolder(view)
        }

        @JvmStatic
        fun onCreateView(viewGroup: ViewGroup, resource: Int): RecyclerViewHolder {
            return RecyclerViewHolder(
                LayoutInflater.from(viewGroup.context).inflate(resource, viewGroup, false)
            )
        }
    }
}