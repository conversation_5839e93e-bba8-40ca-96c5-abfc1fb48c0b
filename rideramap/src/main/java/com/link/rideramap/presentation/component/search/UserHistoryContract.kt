package com.link.rideramap.presentation.component.search

import android.provider.BaseColumns

object UserHistoryContract {
    // 定义表的内容
    object HistoryEntry : BaseColumns {
        const val TABLE_NAME = "history"
        const val COLUMN_NAME_ID = "id"
        const val COLUMN_NAME_AD_CODE = "ad_code"
        const val COLUMN_NAME_DISTRICT = "district"
        const val COLUMN_NAME_NAME = "name"
        const val COLUMN_NAME_POI_ID = "poi_id"
        const val COLUMN_NAME_POINT_LATITUDE = "point_latitude"
        const val COLUMN_NAME_POINT_LONGITUDE = "point_longitude"
        const val COLUMN_NAME_TYPE = "type"
    }
}