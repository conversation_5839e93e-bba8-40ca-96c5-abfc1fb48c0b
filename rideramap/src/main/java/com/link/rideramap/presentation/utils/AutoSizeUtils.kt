package com.link.rideramap.presentation.utils

import android.content.Context
import android.content.res.Resources
import android.util.TypedValue

/**
 * Created on 2023/1/12.
 * <AUTHOR>
 */
internal object AutoSizeUtils {
    fun dp2px(context: Context, value: Float): Int {
        return (TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            value,
            context.resources.displayMetrics
        ) + 0.5f).toInt()
    }

    fun sp2px(context: Context, value: Float): Int {
        return (TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_SP,
            value,
            context.resources.displayMetrics
        ) + 0.5f).toInt()
    }

    fun pt2px(context: Context, value: Float): Int {
        return (TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_PT,
            value,
            context.resources.displayMetrics
        ) + 0.5f).toInt()
    }

    fun in2px(context: Context, value: Float): Int {
        return (TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_IN,
            value,
            context.resources.displayMetrics
        ) + 0.5f).toInt()
    }

    fun mm2px(context: Context, value: Float): Int {
        return (TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_MM,
            value,
            context.resources.displayMetrics
        ) + 0.5f).toInt()
    }


    fun updateResources(context: Context) {
        val resources: Resources = context.resources
        val configuration = resources.configuration
        val displayMetrics = resources.displayMetrics
        resources.updateConfiguration(configuration, displayMetrics)
        context.applicationContext.resources.updateConfiguration(configuration, displayMetrics)
    }
}