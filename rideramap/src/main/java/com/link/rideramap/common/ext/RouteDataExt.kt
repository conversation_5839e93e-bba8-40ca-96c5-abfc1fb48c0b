package com.link.rideramap.common.ext

import com.link.rideramap.core.search.domain.entity.RouteData
import java.text.DecimalFormat

/**
 * <AUTHOR>
 * @date 2022/6/29
 */

fun RouteData.formatTime(): Int {
    return allTime / 60
}

fun RouteData.formatDistance(): String {
    val distance = when {
        allDistance < 1000 -> "$allDistance m"
        allDistance < 1000000 ->
            "${DecimalFormat("0.0").format(allDistance.toDouble() / 1000.0)} km"
        else -> "${DecimalFormat("0").format(allDistance.toDouble() / 1000.0)} km"
    }
    return distance
}