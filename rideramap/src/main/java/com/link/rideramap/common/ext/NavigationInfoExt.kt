package com.link.rideramap.common.ext

import com.link.rideramap.api.dto.NavigationInfo
import com.link.rideramap.core.navigation.utils.NaviUtil
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.*

/**
 * <AUTHOR>
 * @date 2022/6/29
 */

fun NavigationInfo.formatTime(): String {
    val currentTime = System.currentTimeMillis() + pathRetainTime * 1000
    val timeFormat = SimpleDateFormat("HH点mm分钟到达", Locale.CHINA)
    return timeFormat.format(currentTime)
}

fun NavigationInfo.formatPathDistance(): String {
    val distance = when {
        pathRetainDistance < 1000 -> "$pathRetainDistance 米"
        pathRetainDistance < 1000000 ->
            " ${DecimalFormat("0.0").format(pathRetainDistance.toDouble() / 1000.0)}公里"
        else -> "${DecimalFormat("0").format(pathRetainDistance.toDouble() / 1000.0)}公里"
    }

    val minute = pathRetainTime / 60
    val time = when {
        minute > 0 -> "${minute}分钟"
        else -> "1分钟"
    }
    return time.plus("/ $distance").plus("/ ${routeRemainLightCount}个红绿灯")
}

fun NavigationInfo.formatStepRetainDistance(): String {
    val distance = when {
        curStepRetainDistance < 1000 -> "${curStepRetainDistance}m"
        curStepRetainDistance < 1000000 ->
            " ${DecimalFormat("0.0").format(curStepRetainDistance.toDouble() / 1000.0)}km"
        else -> "${DecimalFormat("0").format(curStepRetainDistance.toDouble() / 1000.0)}km"
    }
    return distance
}

fun NavigationInfo.formatCurRoadDetail(): String {
    return "$currentRoadName ${NaviUtil.AMapNaviIcon.getByType(iconType)?.desc} $nextRoadName"
}

fun NavigationInfo.getTime(): String {
    val minute = pathRetainTime / 60
    val time = when {
        minute > 0 -> "${minute}分钟"
        else -> "1分钟"
    }
    return time
}

fun NavigationInfo.getDistance(): String {
    val distance = when {
        pathRetainDistance < 1000 -> "$pathRetainDistance 米"
        pathRetainDistance < 1000000 ->
            " ${DecimalFormat("0.0").format(pathRetainDistance.toDouble() / 1000.0)}公里"
        else -> "${DecimalFormat("0").format(pathRetainDistance.toDouble() / 1000.0)}公里"
    }
    return distance
}
