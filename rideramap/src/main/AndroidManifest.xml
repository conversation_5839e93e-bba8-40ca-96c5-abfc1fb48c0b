<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!--location-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />


    <application>
        <provider
            android:name="androidx.startup.InitializationProvider"
            tools:node="merge"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false">
            <meta-data
                android:name="com.link.rideramap.utils.initializer.RiderAmapInitializer"
                android:value="androidx.startup" />
        </provider>

        <service android:name="com.amap.api.location.APSService" />
    </application>
</manifest>