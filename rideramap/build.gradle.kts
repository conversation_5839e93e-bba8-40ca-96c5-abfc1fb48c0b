import org.jetbrains.dokka.gradle.DokkaTask

plugins {
    id("com.android.library")
    id("org.jetbrains.kotlin.android")
    id("org.jetbrains.dokka")
}

android {
    namespace = "com.link.rideramap"
    compileSdk = 35

    defaultConfig {
        minSdk = 24

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            //设置支持的SO库架构（开发者可以根据需要，选择一个或多个平台的so）
            //noinspection ChromeOsAbiSupport
            abiFilters += listOf("armeabi-v7a", "arm64-v8a")
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }

    buildFeatures {
        viewBinding = true
    }

    libraryVariants.all {
        outputs.all {
            packageLibraryProvider {
                archiveFileName.set("${project.name}_1.0.0.aar")
            }
        }
    }

    lint {
        disable += setOf(
            "BlockedPrivateApi",
            "DiscouragedPrivateApi",
            "PrivateApi",
            "SoonBlockedPrivateApi"
        )
    }
}

tasks.withType<DokkaTask>().configureEach {
    dokkaSourceSets {
        named("main") {
            noStdlibLink.set(false)
            noJdkLink.set(false)
            noAndroidSdkLink.set(false)

            perPackageOption {
                matchingRegex.set("(.*?)")
                suppress.set(true)
            }

            perPackageOption {
                matchingRegex.set("com.link.rideramap.api.*")
                suppress.set(false)
            }
        }
    }
}

dependencies {
    // 高德地图
    implementation(libs.amap)
    implementation(libs.amap.search)

    // Android Kotlin 核心库
    implementation(libs.androidx.core.ktx)
    // Android UI 库
    implementation(libs.androidx.appcompat)
    implementation(libs.google.material)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.test.ext)
    androidTestImplementation(libs.espresso.core)
}