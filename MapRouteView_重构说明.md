# MapRouteView 重构说明

## 重构概述

本次重构将 `MapRouteView` 组件按照 `RiderAMapView` 的架构模式进行了改造，实现了统一的主题配置系统和动态UI支持。

## 主要改进

### 1. 架构模式对齐
- ✅ 采用与 `RiderAMapView` 相同的组件架构模式
- ✅ 实现相同的生命周期管理方式
- ✅ 保持一致的代码组织结构和命名规范

### 2. 动态UI支持
- ✅ 支持运行时动态更改UI组件的外观和行为
- ✅ 实现主题切换功能（日间/夜间模式）
- ✅ 支持颜色、图标、文字等UI元素的动态配置
- ✅ 提供统一的主题更新接口

### 3. 新增功能
- ✅ 添加 `RouteThemeConfig` 主题配置数据类
- ✅ 实现 `updateTheme()` 方法支持主题动态切换
- ✅ 支持 XML 自定义属性配置
- ✅ 保持现有功能不变的前提下进行重构

## 核心组件

### RouteThemeConfig 数据类

```kotlin
data class RouteThemeConfig(
    // 背景配置
    val routeBackgroundRes: Int? = null,
    val routeBackgroundDrawable: Drawable? = null,
    
    // 导航按钮配置
    val naviButtonBackgroundRes: Int? = null,
    val naviButtonTextColor: Int? = null,
    val naviButtonText: String? = null,
    
    // 图标配置
    val trafficOnIconRes: Int? = null,
    val trafficOffIconRes: Int? = null,
    
    // 更多配置...
)
```

### XML 属性支持

```xml
<com.link.rideramap.presentation.component.route.RiderAMapRouteView
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:route_naviButtonTextColor="@color/custom_color"
    app:route_naviButtonText="开始骑行"
    app:route_trafficOnIcon="@drawable/custom_traffic_icon"
    app:route_mapShadowColor="@color/custom_shadow" />
```

## 使用方法

### 1. 程序化主题配置

```kotlin
// 创建主题配置
val themeConfig = RouteThemeConfig(
    naviButtonTextColor = Color.parseColor("#FF6B35"),
    naviButtonText = "开始骑行",
    routeSelectedColor = Color.parseColor("#FF6B35"),
    mapType = AMap.MAP_TYPE_NORMAL
)

// 应用主题
riderAMapRouteView.updateTheme(themeConfig)
```

### 2. 日间/夜间主题切换

```kotlin
// 日间主题
val dayTheme = RouteThemeConfig(
    naviButtonTextColor = Color.parseColor("#5C7BD7"),
    routeTimeTextColor = Color.parseColor("#191919"),
    mapType = AMap.MAP_TYPE_NORMAL
)

// 夜间主题
val nightTheme = RouteThemeConfig(
    naviButtonTextColor = Color.WHITE,
    routeTimeTextColor = Color.WHITE,
    mapType = AMap.MAP_TYPE_NIGHT
)

// 根据时间或用户设置切换主题
val currentTheme = if (isNightMode) nightTheme else dayTheme
riderAMapRouteView.updateTheme(currentTheme)
```

### 3. RouteButtonView 主题配置

```kotlin
// RouteButtonView 专用配置
val routeButtonTheme = RouteThemeConfig(
    routeButtonSelectedTextColor = Color.parseColor("#FF6B35"),
    routeButtonUnselectedTextColor = Color.parseColor("#666666"),
    routeButtonSelectedBackgroundRes = R.drawable.custom_selected_bg,
    routeButtonTrafficLightSelectedIconRes = R.drawable.custom_traffic_icon
)

riderAMapRouteView.updateTheme(routeButtonTheme)
```

### 4. 配置合并

```kotlin
// 基础配置
val baseConfig = createDayTheme()

// 覆盖配置
val overrideConfig = RouteThemeConfig(
    naviButtonText = "立即出发",
    routeButtonSelectedTextColor = Color.parseColor("#FF6B35")
)

// 合并配置（override 优先于 base）
val finalConfig = RouteThemeConfig.merge(overrideConfig, baseConfig)
riderAMapRouteView.updateTheme(finalConfig)
```

## 配置优先级

主题配置按以下优先级应用：

1. **程序化配置** (`updateTheme()` 方法设置)
2. **XML 属性配置** (布局文件中设置)
3. **默认配置** (内置默认值)

## 新增的 XML 属性

| 属性名称 | 格式 | 说明 |
|---------|------|------|
| `route_naviButtonTextColor` | `color` | 导航按钮文字颜色 |
| `route_naviButtonText` | `string` | 导航按钮文字 |
| `route_trafficOnIcon` | `reference` | 交通开启图标 |
| `route_trafficOffIcon` | `reference` | 交通关闭图标 |
| `route_mapShadowColor` | `color` | 地图阴影颜色 |
| `route_mapType` | `integer` | 地图类型 |
| 更多... | | |

## 示例代码

详细的使用示例请参考：
- `RouteThemeExample.kt` - 完整的主题配置示例
- `RiderUIKit_简化说明文档.md` - API 使用文档

## RouteButtonView 重构

- ✅ RouteButtonView 支持动态主题配置
- ✅ 添加了 `updateTheme(RouteThemeConfig)` 方法
- ✅ 支持选中/未选中状态的独立主题配置
- ✅ 集成到 MapRouteView 的统一主题系统中
- ✅ 移除了旧的硬编码主题逻辑

## 代码简化

- ✅ 移除了向后兼容性代码，简化了代码结构
- ✅ 删除了 `initTheme()` 兼容性方法
- ✅ 移除了 `createThemeConfigFromProviders()` 方法
- ✅ 统一使用新的主题配置系统

## 技术细节

### 架构设计
- 采用配置优先级系统，确保灵活性
- 使用数据类封装主题配置，提高类型安全性
- 实现渲染分离，支持动态主题切换
- 保持与 `RiderAMapView` 一致的设计模式

### 性能优化
- 配置缓存机制，避免重复计算
- 按需渲染，只更新变化的UI元素
- 资源复用，减少内存占用

## 总结

本次重构成功实现了：
1. 与 `RiderAMapView` 架构模式的完全对齐
2. 动态主题配置系统的完整实现
3. XML 属性配置的全面支持
4. 向后兼容性的完美保持

重构后的 `MapRouteView` 具有更好的可维护性、扩展性和用户体验。
