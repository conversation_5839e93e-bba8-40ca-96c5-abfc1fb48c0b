# RiderSearchView 重构指南

## 概述

本文档描述了将 `BaseSearchDialogFragment` 中的 `SearchBinding` 重构为独立自定义 View 组件 `RiderSearchView` 的完整方案。

## 重构目标

1. **提高代码复用性**：将搜索功能封装为独立组件，可在多个地方使用
2. **简化复杂度**：减少 `BaseSearchDialogFragment` 的代码复杂度
3. **增强可维护性**：独立的组件更容易维护和测试
4. **保持一致性**：遵循项目中 `RiderAMapView` 的设计模式

## 重构内容

### 1. 新增文件

#### 核心组件
- `RiderSearchView.kt` - 主要的搜索视图组件
- `RiderSearchViewConfig.kt` - 配置类，支持主题和行为定制
- `rider_search_view.xml` - 搜索组件的布局文件

#### 支持文件
- `SearchExtensions.kt` - 扩展函数（文本变化监听、防抖点击等）
- `SearchViewModel.kt` - 搜索相关的 ViewModel 接口和实现
- `SearchThemeManager.kt` - 主题管理器（简化版本）

#### 重构示例
- `RefactoredSearchDialogFragment.kt` - 使用新组件的对话框实现
- `refactored_search_dialog.xml` - 重构后的对话框布局
- `RiderSearchViewUsageExample.kt` - 使用示例和最佳实践

#### 属性定义
- 在 `attrs.xml` 中添加了 `RiderSearchView` 的自定义属性

### 2. 核心特性

#### RiderSearchView 组件特性
- ✅ 搜索输入和实时搜索
- ✅ 搜索历史记录管理
- ✅ 搜索结果展示
- ✅ 关键字高亮显示
- ✅ 主题切换支持
- ✅ 键盘管理
- ✅ 防抖搜索
- ✅ 自定义配置支持

#### 配置选项
```kotlin
RiderSearchViewConfig(
    searchHint: String,           // 搜索提示文本
    searchTextColor: Int,         // 搜索文本颜色
    searchHintColor: Int,         // 提示文本颜色
    backgroundColor: Int,         // 背景颜色
    highlightColor: Int,          // 高亮颜色
    showHistory: Boolean,         // 是否显示历史记录
    maxHistoryCount: Int,         // 最大历史记录数
    debounceTime: Long,          // 搜索防抖时间
    enableHighlight: Boolean,     // 是否启用高亮
    // ... 更多配置选项
)
```

## 使用方式

### 1. 在 XML 中使用

```xml
<com.link.rideramap.presentation.component.search.RiderSearchView
    android:id="@+id/rider_search_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:search_hint="请输入搜索内容"
    app:search_text_color="#202229"
    app:show_history="true"
    app:max_history_count="10" />
```

### 2. 在代码中使用

```kotlin
// 基础使用
val searchView = findViewById<RiderSearchView>(R.id.rider_search_view)
searchView.setLifecycleOwner(this)
searchView.setSearchCallback(object : RiderSearchView.SearchCallback {
    override fun onSearchTextChanged(query: String) {
        // 处理搜索文本变化
    }
    
    override fun onSearchResultSelected(address: SearchAddress) {
        // 处理搜索结果选择
    }
    
    // ... 其他回调方法
})

// 配置更新
val config = RiderSearchViewConfig.Builder()
    .searchHint("自定义提示")
    .showHistory(true)
    .maxHistoryCount(15)
    .build()
searchView.updateConfig(config)
```

### 3. 在对话框中使用

```kotlin
val dialog = RefactoredSearchDialogFragment.newInstance(
    onAddressSelected = { address ->
        // 处理地址选择
    },
    config = RiderSearchViewConfig.createDefault()
)
dialog.show(supportFragmentManager, "search_dialog")
```

## 主题支持

### 预定义主题
```kotlin
// 默认主题
RiderSearchViewConfig.createDefault()

// 深色主题
RiderSearchViewConfig.createDarkTheme()

// 浅色主题
RiderSearchViewConfig.createLightTheme()

// 简洁模式（无历史记录）
RiderSearchViewConfig.createSimpleMode()

// 高性能模式
RiderSearchViewConfig.createPerformanceMode()
```

### 自定义主题
```kotlin
val customConfig = RiderSearchViewConfig.Builder()
    .searchTextColor(Color.BLACK)
    .backgroundColor(Color.WHITE)
    .highlightColor(Color.BLUE)
    .build()
```

## 迁移指南

### 从 BaseSearchDialogFragment 迁移

1. **替换布局**：
   - 原来：使用 `SearchBinding`
   - 现在：使用 `RiderSearchView` 组件

2. **简化逻辑**：
   - 原来：在 Fragment 中处理所有搜索逻辑
   - 现在：通过回调接口处理，逻辑更清晰

3. **配置方式**：
   - 原来：硬编码配置
   - 现在：通过 `RiderSearchViewConfig` 配置

### 兼容性

- ✅ 保持原有的 `SearchAddress` 数据结构
- ✅ 保持原有的搜索功能
- ✅ 支持原有的主题系统
- ✅ 保持原有的历史记录功能

## 优势

### 1. 代码复用性
- 搜索组件可以在多个地方使用
- 减少重复代码

### 2. 可维护性
- 独立的组件更容易测试
- 职责分离，代码更清晰

### 3. 可扩展性
- 通过配置类支持各种定制需求
- 支持主题切换

### 4. 性能优化
- 防抖搜索减少不必要的请求
- 可配置的性能模式

## 最佳实践

1. **生命周期管理**：
   ```kotlin
   searchView.setLifecycleOwner(this) // 重要：设置生命周期拥有者
   ```

2. **配置复用**：
   ```kotlin
   // 创建可复用的配置
   val appSearchConfig = RiderSearchViewConfig.createDefault()
   ```

3. **错误处理**：
   ```kotlin
   // 在回调中处理错误
   override fun onSearchTextChanged(query: String) {
       try {
           viewModel.search(query)
       } catch (e: Exception) {
           // 处理错误
       }
   }
   ```

4. **性能优化**：
   ```kotlin
   // 对于简单场景，使用性能模式
   val performanceConfig = RiderSearchViewConfig.createPerformanceMode()
   ```

## 注意事项

1. **依赖管理**：确保项目中包含必要的依赖（协程、ViewBinding 等）
2. **主题兼容**：如果项目有自定义主题系统，需要适配 `SearchThemeManager`
3. **数据库**：历史记录功能依赖 `UserHistoryDao`，确保数据库正确初始化
4. **权限**：如果搜索涉及网络请求，确保有相应权限

## 总结

通过这次重构，我们成功地将复杂的搜索功能封装为一个独立、可复用的组件。新的 `RiderSearchView` 不仅保持了原有功能的完整性，还提供了更好的可维护性和扩展性。

重构后的代码结构更清晰，符合单一职责原则，为后续的功能扩展和维护奠定了良好的基础。
